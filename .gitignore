# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this project gitignore.  For a PyCharm
#  project, it is generally recommended to include the cache and path variables, but
#  exclude the .idea/ folder.
#  https://github.com/github/gitignore/blob/main/Global/JetBrains.gitignore
.idea/

# VS Code
.vscode/

# Django specific
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Django media files (user uploads) - 重要：忽略所有用户上传的文件
media/
!media/.gitkeep

# Django static files
staticfiles/
static_root/
collected_static/

# Django cache
django_cache/

# Django sessions
django_session/

# Django migrations (uncomment if you want to ignore migrations)
# */migrations/*.py
# !*/migrations/__init__.py

# Django secret key and sensitive settings
secret_key.txt
.secret_key

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Node.js (if you have any frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
# Keep Docker configuration files but ignore data and logs
docker/*/data/
docker/*/logs/
docker/mariadb/data/
docker/nginx/logs/
# Docker volumes and runtime data
docker-compose.override.yml
.docker/
# Keep .dockerignore file
# .dockerignore

# uv specific
.uv/

# Backup files
*.bak
*.backup

# Local configuration files
local_config.py
local_settings.py

# SSL certificates
*.pem
*.key
*.crt

# Redis dump file
dump.rdb

# Elasticsearch
elasticsearch/

# Coverage reports
htmlcov/
.coverage
coverage.xml

# Pytest
.pytest_cache/

# Jupyter
.ipynb_checkpoints/

# MacOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~
