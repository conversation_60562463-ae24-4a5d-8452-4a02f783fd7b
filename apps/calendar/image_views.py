from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON>art<PERSON>ars<PERSON>, FormParser
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from django.db.models import Q
from .image_models import PrenatalImage, ImageUploadSession
from .image_serializers import (
    PrenatalImageSerializer,
    PrenatalImageUploadSerializer,
    PrenatalImageListSerializer,
    ImageUploadSessionSerializer,
    BatchImageUploadSerializer
)


class PrenatalImageViewSet(viewsets.ModelViewSet):
    """产检图片视图集"""
    
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['image_type', 'checkup']
    search_fields = ['title', 'description']
    ordering_fields = ['created_at', 'file_size', 'image_type']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """只返回当前用户的图片"""
        return PrenatalImage.objects.filter(user=self.request.user).select_related('checkup')
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return PrenatalImageListSerializer
        elif self.action == 'create':
            return PrenatalImageUploadSerializer
        return PrenatalImageSerializer
    
    @action(detail=False, methods=['post'])
    def batch_upload(self, request):
        """批量上传图片"""
        serializer = BatchImageUploadSerializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        
        result = serializer.save()
        
        return Response({
            'message': '批量上传完成',
            'session_id': result['session'].id,
            'success_count': result['success_count'],
            'failed_count': result['failed_count'],
            'total_count': len(request.data.getlist('images')),
            'created_images': PrenatalImageListSerializer(
                result['created_images'], 
                many=True, 
                context={'request': request}
            ).data
        }, status=status.HTTP_201_CREATED)
    
    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """按类型获取图片"""
        image_type = request.query_params.get('type')
        if not image_type:
            return Response({'error': '请提供图片类型参数'}, status=status.HTTP_400_BAD_REQUEST)
        
        images = self.get_queryset().filter(image_type=image_type)
        serializer = PrenatalImageListSerializer(images, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_checkup(self, request):
        """按产检记录获取图片"""
        checkup_id = request.query_params.get('checkup_id')
        if not checkup_id:
            return Response({'error': '请提供产检记录ID'}, status=status.HTTP_400_BAD_REQUEST)
        
        images = self.get_queryset().filter(checkup_id=checkup_id)
        serializer = PrenatalImageListSerializer(images, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def recent(self, request):
        """获取最近上传的图片"""
        days = int(request.query_params.get('days', 7))
        since_date = timezone.now() - timezone.timedelta(days=days)
        
        images = self.get_queryset().filter(created_at__gte=since_date)
        serializer = PrenatalImageListSerializer(images, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取图片统计信息"""
        queryset = self.get_queryset()
        
        # 按类型统计
        type_stats = {}
        for choice in PrenatalImage.IMAGE_TYPE_CHOICES:
            type_key = choice[0]
            type_name = choice[1]
            count = queryset.filter(image_type=type_key).count()
            type_stats[type_key] = {
                'name': type_name,
                'count': count
            }
        
        # 总体统计
        total_count = queryset.count()
        total_size = sum(img.file_size for img in queryset)
        
        # 最近7天上传数量
        recent_date = timezone.now() - timezone.timedelta(days=7)
        recent_count = queryset.filter(created_at__gte=recent_date).count()
        
        return Response({
            'total_count': total_count,
            'total_size': total_size,
            'total_size_human': self._format_file_size(total_size),
            'recent_count': recent_count,
            'type_statistics': type_stats
        })
    
    def _format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
    
    def perform_destroy(self, instance):
        """删除图片时的额外处理"""
        # 如果图片关联了产检记录，从产检记录的图片列表中移除
        if instance.checkup and instance.image_url:
            checkup = instance.checkup
            if instance.image_url in checkup.checkup_images:
                checkup.checkup_images.remove(instance.image_url)
                checkup.save(update_fields=['checkup_images'])
        
        # 删除图片记录（会自动删除文件）
        super().perform_destroy(instance)


class ImageUploadSessionViewSet(viewsets.ReadOnlyModelViewSet):
    """图片上传会话视图集"""
    
    serializer_class = ImageUploadSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status', 'checkup']
    ordering_fields = ['created_at', 'completed_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """只返回当前用户的上传会话"""
        return ImageUploadSession.objects.filter(user=self.request.user).select_related('checkup')
    
    @action(detail=True, methods=['get'])
    def images(self, request, pk=None):
        """获取上传会话中的所有图片"""
        session = self.get_object()
        
        # 通过时间范围查找该会话期间上传的图片
        start_time = session.created_at
        end_time = session.completed_at or timezone.now()
        
        images = PrenatalImage.objects.filter(
            user=session.user,
            created_at__gte=start_time,
            created_at__lte=end_time
        )
        
        # 如果会话关联了产检记录，进一步筛选
        if session.checkup:
            images = images.filter(checkup=session.checkup)
        
        serializer = PrenatalImageListSerializer(images, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """获取活跃的上传会话"""
        active_sessions = self.get_queryset().filter(
            status__in=['pending', 'uploading']
        )
        serializer = self.get_serializer(active_sessions, many=True)
        return Response(serializer.data)


# 辅助视图函数
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from django.http import JsonResponse


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def quick_upload(request):
    """快速上传单张图片"""
    if 'image' not in request.FILES:
        return JsonResponse({'error': '请选择图片文件'}, status=400)
    
    try:
        # 创建图片记录
        image = PrenatalImage.objects.create(
            user=request.user,
            image=request.FILES['image'],
            image_type=request.data.get('image_type', 'other'),
            title=request.data.get('title', ''),
            description=request.data.get('description', '')
        )
        
        return JsonResponse({
            'success': True,
            'image_id': str(image.id),
            'image_url': image.image_url,
            'message': '图片上传成功'
        })
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def image_types(request):
    """获取图片类型选项"""
    types = [
        {'value': choice[0], 'label': choice[1]}
        for choice in PrenatalImage.IMAGE_TYPE_CHOICES
    ]
    return JsonResponse({'image_types': types})
