from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from django.db import models
from .models import PrenatalItem, CustomPrenatalItem, PrenatalCheckup, Task
from .serializers import (
    PrenatalItemSerializer,
    CustomPrenatalItemSerializer,
    PrenatalCheckupSerializer,
    PrenatalCheckupListSerializer,
    TaskSerializer,
    TaskListSerializer
)


class PrenatalItemViewSet(viewsets.ReadOnlyModelViewSet):
    """标准产检项目视图集（只读）"""

    queryset = PrenatalItem.objects.all()
    serializer_class = PrenatalItemSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['start_week', 'end_week']
    search_fields = ['name', 'content']
    ordering_fields = ['start_week', 'end_week', 'name', 'created_at']
    ordering = ['start_week', 'name']

    @action(detail=False, methods=['get'])
    def by_week(self, request):
        """根据孕周获取适用的产检项目"""
        week = request.query_params.get('week')
        if not week:
            return Response({'error': '请提供孕周参数'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            week = int(week)
            if week < 1 or week > 42:
                return Response({'error': '孕周必须在1-42之间'}, status=status.HTTP_400_BAD_REQUEST)
        except ValueError:
            return Response({'error': '孕周必须是数字'}, status=status.HTTP_400_BAD_REQUEST)

        # 查询适用的产检项目
        items = self.queryset.filter(
            models.Q(start_week__isnull=True) | models.Q(start_week__lte=week),
            models.Q(end_week__isnull=True) | models.Q(end_week__gte=week)
        )

        serializer = self.get_serializer(items, many=True)
        return Response(serializer.data)


class CustomPrenatalItemViewSet(viewsets.ModelViewSet):
    """自定义产检项目视图集"""

    serializer_class = CustomPrenatalItemSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['start_week', 'end_week']
    search_fields = ['name', 'content']
    ordering_fields = ['start_week', 'end_week', 'name', 'created_at']
    ordering = ['start_week', 'name']

    def get_queryset(self):
        """只返回当前用户创建的自定义项目"""
        return CustomPrenatalItem.objects.filter(created_by=self.request.user)


class PrenatalCheckupViewSet(viewsets.ModelViewSet):
    """产检记录视图集"""

    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['date', 'time']
    search_fields = ['location', 'preparation_notes', 'checkup_notes']
    ordering_fields = ['date', 'time', 'created_at']
    ordering = ['-date', '-time']

    def get_queryset(self):
        """只返回当前用户的产检记录"""
        return PrenatalCheckup.objects.filter(user=self.request.user).prefetch_related(
            'prenatal_items', 'custom_prenatal_items'
        )

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return PrenatalCheckupListSerializer
        return PrenatalCheckupSerializer

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """获取即将到来的产检"""
        from datetime import datetime, date, time

        now = timezone.now()
        today = now.date()
        current_time = now.time()

        # 查询今天之后的产检，或今天但时间在当前时间之后的产检
        upcoming_checkups = self.get_queryset().filter(
            models.Q(date__gt=today) |
            models.Q(date=today, time__gt=current_time)
        ).order_by('date', 'time')

        serializer = PrenatalCheckupListSerializer(upcoming_checkups, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def past_due(self, request):
        """获取已过期的产检"""
        from datetime import datetime, date, time

        now = timezone.now()
        today = now.date()
        current_time = now.time()

        # 查询今天之前的产检，或今天但时间在当前时间之前的产检
        past_due_checkups = self.get_queryset().filter(
            models.Q(date__lt=today) |
            models.Q(date=today, time__lt=current_time)
        ).order_by('-date', '-time')

        serializer = PrenatalCheckupListSerializer(past_due_checkups, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def add_image(self, request, pk=None):
        """添加产检图片（通过图片ID）"""
        checkup = self.get_object()
        image_id = request.data.get('image_id')
        image_url = request.data.get('image_url')

        if image_id:
            # 通过图片ID关联
            try:
                from .image_models import PrenatalImage
                image = PrenatalImage.objects.get(id=image_id, user=request.user)
                image.checkup = checkup
                image.save()

                # 同时添加到checkup_images列表
                if image.image_url and image.image_url not in checkup.checkup_images:
                    checkup.add_image(image.image_url)

                return Response({'message': '图片关联成功', 'image_url': image.image_url})
            except Exception as e:
                return Response({'error': f'图片关联失败: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

        elif image_url:
            # 通过URL添加（兼容旧方式）
            checkup.add_image(image_url)
            return Response({'message': '图片添加成功'})

        else:
            return Response({'error': '请提供图片ID或图片URL'}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def remove_image(self, request, pk=None):
        """移除产检图片"""
        checkup = self.get_object()
        image_id = request.data.get('image_id')
        image_url = request.data.get('image_url')

        if image_id:
            # 通过图片ID移除关联
            try:
                from .image_models import PrenatalImage
                image = PrenatalImage.objects.get(id=image_id, user=request.user, checkup=checkup)
                image.checkup = None
                image.save()

                # 同时从checkup_images列表移除
                if image.image_url and image.image_url in checkup.checkup_images:
                    checkup.remove_image(image.image_url)

                return Response({'message': '图片关联已移除'})
            except Exception as e:
                return Response({'error': f'移除图片关联失败: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

        elif image_url:
            # 通过URL移除（兼容旧方式）
            checkup.remove_image(image_url)
            return Response({'message': '图片移除成功'})

        else:
            return Response({'error': '请提供图片ID或图片URL'}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def images(self, request, pk=None):
        """获取产检记录的所有图片"""
        checkup = self.get_object()

        # 获取关联的图片文件
        from .image_models import PrenatalImage
        from .image_serializers import PrenatalImageListSerializer

        images = PrenatalImage.objects.filter(checkup=checkup)
        serializer = PrenatalImageListSerializer(images, many=True, context={'request': request})

        return Response({
            'checkup_id': checkup.id,
            'image_files': serializer.data,
            'image_urls': checkup.checkup_images  # JSON字段中的URL列表
        })


class TaskViewSet(viewsets.ModelViewSet):
    """任务管理视图集 - 支持自定义任务和吃药提醒"""

    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['task_type', 'is_all_day']
    search_fields = ['title', 'medicine_name', 'notes', 'location']
    ordering_fields = ['start_time', 'medication_time', 'created_at', 'updated_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """只返回当前用户的任务"""
        queryset = Task.objects.filter(user=self.request.user)

        # 支持日期范围查询
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            try:
                from datetime import datetime
                start_datetime = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                queryset = queryset.filter(
                    models.Q(start_time__gte=start_datetime) |
                    models.Q(start_time__isnull=True)
                )
            except ValueError:
                pass

        if end_date:
            try:
                from datetime import datetime
                end_datetime = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                queryset = queryset.filter(
                    models.Q(end_time__lte=end_datetime) |
                    models.Q(end_time__isnull=True)
                )
            except ValueError:
                pass

        return queryset

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return TaskListSerializer
        return TaskSerializer

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """获取即将到来的任务"""
        upcoming_tasks = []

        for task in self.get_queryset():
            if task.is_upcoming:
                upcoming_tasks.append(task)

        # 按时间排序
        if upcoming_tasks:
            upcoming_tasks.sort(key=lambda t: t.start_time or t.medication_time or t.created_at)

        serializer = TaskListSerializer(upcoming_tasks, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def past(self, request):
        """获取已过期的任务"""
        past_tasks = []

        for task in self.get_queryset():
            if task.is_past:
                past_tasks.append(task)

        # 按时间倒序排序
        if past_tasks:
            past_tasks.sort(key=lambda t: t.end_time or t.medication_time or t.created_at, reverse=True)

        serializer = TaskListSerializer(past_tasks, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def ongoing(self, request):
        """获取正在进行中的任务（仅适用于自定义任务）"""
        ongoing_tasks = []

        for task in self.get_queryset():
            if task.is_ongoing:
                ongoing_tasks.append(task)

        serializer = TaskListSerializer(ongoing_tasks, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """按任务类型获取任务"""
        task_type = request.query_params.get('type')
        if not task_type:
            return Response({'error': '请提供任务类型参数'}, status=status.HTTP_400_BAD_REQUEST)

        if task_type not in [Task.TaskType.CUSTOM, Task.TaskType.MEDICATION]:
            return Response({'error': '无效的任务类型'}, status=status.HTTP_400_BAD_REQUEST)

        tasks = self.get_queryset().filter(task_type=task_type)
        serializer = TaskListSerializer(tasks, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def today_medications(self, request):
        """获取今天的吃药提醒"""
        from datetime import date

        today = date.today()
        today_weekday = today.isoweekday()  # 1=周一，7=周日

        # 查询今天在周期内的吃药提醒
        medication_tasks = self.get_queryset().filter(
            task_type=Task.TaskType.MEDICATION,
            weekly_schedule__contains=[today_weekday]
        ).order_by('medication_time')

        serializer = TaskListSerializer(medication_tasks, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def weekly_schedule(self, request):
        """获取本周的任务安排"""
        from datetime import date, timedelta

        # 获取本周的日期范围
        today = date.today()
        start_of_week = today - timedelta(days=today.weekday())  # 周一
        end_of_week = start_of_week + timedelta(days=6)  # 周日

        # 获取本周的自定义任务
        custom_tasks = self.get_queryset().filter(
            task_type=Task.TaskType.CUSTOM,
            start_time__date__gte=start_of_week,
            start_time__date__lte=end_of_week
        ).order_by('start_time')

        # 获取本周的吃药提醒
        medication_tasks = []
        for task in self.get_queryset().filter(task_type=Task.TaskType.MEDICATION):
            # 检查本周哪些天在周期内
            for i in range(7):
                day = start_of_week + timedelta(days=i)
                day_weekday = day.isoweekday()
                if day_weekday in task.weekly_schedule:
                    medication_tasks.append({
                        'task': task,
                        'date': day,
                        'weekday': day_weekday
                    })

        # 序列化数据
        custom_serializer = TaskListSerializer(custom_tasks, many=True, context={'request': request})
        medication_serializer = TaskListSerializer([item['task'] for item in medication_tasks], many=True, context={'request': request})

        return Response({
            'week_start': start_of_week,
            'week_end': end_of_week,
            'custom_tasks': custom_serializer.data,
            'medication_tasks': [
                {
                    'task': medication_serializer.data[i],
                    'date': item['date'],
                    'weekday': item['weekday']
                } for i, item in enumerate(medication_tasks)
            ]
        })
