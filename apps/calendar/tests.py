from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.users.models import UserAccount
from .models import PrenatalItem, CustomPrenatalItem, PrenatalCheckup, Event
import datetime


class PrenatalItemModelTest(TestCase):
    """标准产检项目模型测试"""

    def test_create_prenatal_item(self):
        """测试创建标准产检项目"""
        item = PrenatalItem.objects.create(
            name='血常规',
            content='检查血红蛋白、白细胞等',
            start_week=1,
            end_week=42
        )
        self.assertEqual(item.name, '血常规')
        self.assertEqual(str(item), '血常规 (1-42周)')

    def test_week_range_validation(self):
        """测试孕周范围验证"""
        item = PrenatalItem(
            name='测试项目',
            start_week=20,
            end_week=10  # 错误：开始周大于结束周
        )
        with self.assertRaises(ValidationError):
            item.clean()

    def test_is_applicable_for_week(self):
        """测试孕周适用性检查"""
        item = PrenatalItem.objects.create(
            name='NT检查',
            start_week=11,
            end_week=13
        )
        self.assertTrue(item.is_applicable_for_week(12))
        self.assertFalse(item.is_applicable_for_week(10))
        self.assertFalse(item.is_applicable_for_week(15))


class CustomPrenatalItemModelTest(TestCase):
    """自定义产检项目模型测试"""

    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_custom_prenatal_item(self):
        """测试创建自定义产检项目"""
        item = CustomPrenatalItem.objects.create(
            name='自定义检查',
            content='用户自定义的检查项目',
            created_by=self.user,
            start_week=20,
            end_week=24
        )
        self.assertEqual(item.created_by, self.user)
        self.assertIn('by <EMAIL>', str(item))


class PrenatalCheckupModelTest(TestCase):
    """产检记录模型测试"""

    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.prenatal_item = PrenatalItem.objects.create(
            name='血常规',
            content='检查血红蛋白等'
        )
        self.custom_item = CustomPrenatalItem.objects.create(
            name='自定义检查',
            created_by=self.user
        )

    def test_create_prenatal_checkup(self):
        """测试创建产检记录"""
        future_datetime = timezone.now() + datetime.timedelta(days=7)
        checkup = PrenatalCheckup.objects.create(
            user=self.user,
            date=future_datetime.date(),
            time=future_datetime.time(),
            location='测试医院'
        )
        checkup.prenatal_items.add(self.prenatal_item)
        checkup.custom_prenatal_items.add(self.custom_item)

        self.assertEqual(checkup.user, self.user)
        self.assertEqual(checkup.location, '测试医院')
        self.assertEqual(len(checkup.get_all_items()), 2)

    def test_is_upcoming_property(self):
        """测试即将到来的产检判断"""
        future_datetime = timezone.now() + datetime.timedelta(days=7)
        checkup = PrenatalCheckup.objects.create(
            user=self.user,
            date=future_datetime.date(),
            time=future_datetime.time(),
            location='测试医院'
        )
        self.assertTrue(checkup.is_upcoming)

    def test_add_remove_images(self):
        """测试添加和移除图片"""
        future_datetime = timezone.now() + datetime.timedelta(days=7)
        checkup = PrenatalCheckup.objects.create(
            user=self.user,
            date=future_datetime.date(),
            time=future_datetime.time(),
            location='测试医院'
        )

        # 添加图片
        checkup.add_image('http://example.com/image1.jpg')
        checkup.add_image('http://example.com/image2.jpg')
        self.assertEqual(len(checkup.checkup_images), 2)

        # 移除图片
        checkup.remove_image('http://example.com/image1.jpg')
        self.assertEqual(len(checkup.checkup_images), 1)
        self.assertNotIn('http://example.com/image1.jpg', checkup.checkup_images)


class EventModelTest(TestCase):
    """事项/事件模型测试"""

    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_event(self):
        """测试创建事项"""
        start_time = timezone.now() + datetime.timedelta(days=1, hours=2)
        end_time = start_time + datetime.timedelta(hours=1)

        event = Event.objects.create(
            user=self.user,
            title='产检预约',
            start_time=start_time,
            end_time=end_time,
            location='妇幼保健院',
            travel_duration=datetime.timedelta(minutes=30),
            reminder_minutes=60,
            notes='记得带身份证和医保卡',
            date=start_time.date()
        )

        self.assertEqual(event.user, self.user)
        self.assertEqual(event.title, '产检预约')
        self.assertEqual(event.location, '妇幼保健院')
        self.assertEqual(event.reminder_minutes, 60)
        self.assertEqual(str(event), '产检预约')

    def test_event_time_validation(self):
        """测试事项时间验证"""
        start_time = timezone.now() + datetime.timedelta(days=1)
        end_time = start_time - datetime.timedelta(hours=1)  # 错误：结束时间早于开始时间

        event = Event(
            user=self.user,
            title='测试事项',
            start_time=start_time,
            end_time=end_time,
            date=start_time.date()
        )

        with self.assertRaises(ValidationError):
            event.clean()

    def test_date_consistency_validation(self):
        """测试日期一致性验证"""
        start_time = timezone.now() + datetime.timedelta(days=1)
        wrong_date = start_time.date() + datetime.timedelta(days=1)  # 错误：日期不一致

        event = Event(
            user=self.user,
            title='测试事项',
            start_time=start_time,
            end_time=start_time + datetime.timedelta(hours=1),
            date=wrong_date
        )

        with self.assertRaises(ValidationError):
            event.clean()

    def test_negative_reminder_validation(self):
        """测试负数提醒时间验证"""
        start_time = timezone.now() + datetime.timedelta(days=1)

        event = Event(
            user=self.user,
            title='测试事项',
            start_time=start_time,
            end_time=start_time + datetime.timedelta(hours=1),
            date=start_time.date(),
            reminder_minutes=-10  # 错误：负数提醒时间
        )

        with self.assertRaises(ValidationError):
            event.clean()

    def test_event_status_properties(self):
        """测试事项状态属性"""
        now = timezone.now()

        # 即将到来的事项
        future_start = now + datetime.timedelta(hours=2)
        future_end = future_start + datetime.timedelta(hours=1)
        upcoming_event = Event.objects.create(
            user=self.user,
            title='即将到来的事项',
            start_time=future_start,
            end_time=future_end,
            date=future_start.date()
        )
        self.assertTrue(upcoming_event.is_upcoming)
        self.assertFalse(upcoming_event.is_past)
        self.assertFalse(upcoming_event.is_ongoing)

        # 正在进行的事项
        ongoing_start = now - datetime.timedelta(minutes=30)
        ongoing_end = now + datetime.timedelta(minutes=30)
        ongoing_event = Event.objects.create(
            user=self.user,
            title='正在进行的事项',
            start_time=ongoing_start,
            end_time=ongoing_end,
            date=ongoing_start.date()
        )
        self.assertFalse(ongoing_event.is_upcoming)
        self.assertFalse(ongoing_event.is_past)
        self.assertTrue(ongoing_event.is_ongoing)

        # 已过期的事项
        past_start = now - datetime.timedelta(hours=2)
        past_end = now - datetime.timedelta(hours=1)
        past_event = Event.objects.create(
            user=self.user,
            title='已过期的事项',
            start_time=past_start,
            end_time=past_end,
            date=past_start.date()
        )
        self.assertFalse(past_event.is_upcoming)
        self.assertTrue(past_event.is_past)
        self.assertFalse(past_event.is_ongoing)

    def test_duration_property(self):
        """测试持续时间属性"""
        start_time = timezone.now() + datetime.timedelta(days=1)
        end_time = start_time + datetime.timedelta(hours=2, minutes=30)

        event = Event.objects.create(
            user=self.user,
            title='测试事项',
            start_time=start_time,
            end_time=end_time,
            date=start_time.date()
        )

        expected_duration = datetime.timedelta(hours=2, minutes=30)
        self.assertEqual(event.duration, expected_duration)

    def test_get_reminder_time(self):
        """测试获取提醒时间"""
        start_time = timezone.now() + datetime.timedelta(days=1)

        event = Event.objects.create(
            user=self.user,
            title='测试事项',
            start_time=start_time,
            end_time=start_time + datetime.timedelta(hours=1),
            date=start_time.date(),
            reminder_minutes=30
        )

        expected_reminder = start_time - datetime.timedelta(minutes=30)
        self.assertEqual(event.get_reminder_time(), expected_reminder)

    def test_get_departure_time(self):
        """测试获取出发时间"""
        start_time = timezone.now() + datetime.timedelta(days=1)
        travel_duration = datetime.timedelta(minutes=45)

        event = Event.objects.create(
            user=self.user,
            title='测试事项',
            start_time=start_time,
            end_time=start_time + datetime.timedelta(hours=1),
            date=start_time.date(),
            travel_duration=travel_duration
        )

        expected_departure = start_time - travel_duration
        self.assertEqual(event.get_departure_time(), expected_departure)

    def test_optional_fields(self):
        """测试可选字段"""
        start_time = timezone.now() + datetime.timedelta(days=1)

        # 只包含必填字段的事项
        event = Event.objects.create(
            user=self.user,
            title='简单事项',
            start_time=start_time,
            end_time=start_time + datetime.timedelta(hours=1),
            date=start_time.date()
        )

        self.assertEqual(event.location, '')
        self.assertIsNone(event.travel_duration)
        self.assertIsNone(event.reminder_minutes)
        self.assertEqual(event.notes, '')
        self.assertIsNone(event.get_reminder_time())
        self.assertIsNone(event.get_departure_time())
