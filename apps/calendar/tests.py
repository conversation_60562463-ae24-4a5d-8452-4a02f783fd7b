from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError
from rest_framework.test import APITestCase
from rest_framework import status
from apps.users.models import UserAccount
from .models import PrenatalItem, CustomPrenatalItem, PrenatalCheckup, Event, Task
import datetime
from decimal import Decimal


class PrenatalItemModelTest(TestCase):
    """标准产检项目模型测试"""

    def test_create_prenatal_item(self):
        """测试创建标准产检项目"""
        item = PrenatalItem.objects.create(
            name='血常规',
            content='检查血红蛋白、白细胞等',
            start_week=1,
            end_week=42
        )
        self.assertEqual(item.name, '血常规')
        self.assertEqual(str(item), '血常规 (1-42周)')

    def test_week_range_validation(self):
        """测试孕周范围验证"""
        item = PrenatalItem(
            name='测试项目',
            start_week=20,
            end_week=10  # 错误：开始周大于结束周
        )
        with self.assertRaises(ValidationError):
            item.clean()

    def test_is_applicable_for_week(self):
        """测试孕周适用性检查"""
        item = PrenatalItem.objects.create(
            name='NT检查',
            start_week=11,
            end_week=13
        )
        self.assertTrue(item.is_applicable_for_week(12))
        self.assertFalse(item.is_applicable_for_week(10))
        self.assertFalse(item.is_applicable_for_week(15))


class CustomPrenatalItemModelTest(TestCase):
    """自定义产检项目模型测试"""

    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_custom_prenatal_item(self):
        """测试创建自定义产检项目"""
        item = CustomPrenatalItem.objects.create(
            name='自定义检查',
            content='用户自定义的检查项目',
            created_by=self.user,
            start_week=20,
            end_week=24
        )
        self.assertEqual(item.created_by, self.user)
        self.assertIn('by <EMAIL>', str(item))


class PrenatalCheckupModelTest(TestCase):
    """产检记录模型测试"""

    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.prenatal_item = PrenatalItem.objects.create(
            name='血常规',
            content='检查血红蛋白等'
        )
        self.custom_item = CustomPrenatalItem.objects.create(
            name='自定义检查',
            created_by=self.user
        )

    def test_create_prenatal_checkup(self):
        """测试创建产检记录"""
        future_datetime = timezone.now() + datetime.timedelta(days=7)
        checkup = PrenatalCheckup.objects.create(
            user=self.user,
            date=future_datetime.date(),
            time=future_datetime.time(),
            location='测试医院'
        )
        checkup.prenatal_items.add(self.prenatal_item)
        checkup.custom_prenatal_items.add(self.custom_item)

        self.assertEqual(checkup.user, self.user)
        self.assertEqual(checkup.location, '测试医院')
        self.assertEqual(len(checkup.get_all_items()), 2)

    def test_is_upcoming_property(self):
        """测试即将到来的产检判断"""
        future_datetime = timezone.now() + datetime.timedelta(days=7)
        checkup = PrenatalCheckup.objects.create(
            user=self.user,
            date=future_datetime.date(),
            time=future_datetime.time(),
            location='测试医院'
        )
        self.assertTrue(checkup.is_upcoming)

    def test_add_remove_images(self):
        """测试添加和移除图片"""
        future_datetime = timezone.now() + datetime.timedelta(days=7)
        checkup = PrenatalCheckup.objects.create(
            user=self.user,
            date=future_datetime.date(),
            time=future_datetime.time(),
            location='测试医院'
        )

        # 添加图片
        checkup.add_image('http://example.com/image1.jpg')
        checkup.add_image('http://example.com/image2.jpg')
        self.assertEqual(len(checkup.checkup_images), 2)

        # 移除图片
        checkup.remove_image('http://example.com/image1.jpg')
        self.assertEqual(len(checkup.checkup_images), 1)
        self.assertNotIn('http://example.com/image1.jpg', checkup.checkup_images)


class EventModelTest(TestCase):
    """事项/事件模型测试"""

    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_event(self):
        """测试创建事项"""
        start_time = timezone.now() + datetime.timedelta(days=1, hours=2)
        end_time = start_time + datetime.timedelta(hours=1)

        event = Event.objects.create(
            user=self.user,
            title='产检预约',
            start_time=start_time,
            end_time=end_time,
            location='妇幼保健院',
            travel_duration=datetime.timedelta(minutes=30),
            reminder_minutes=60,
            notes='记得带身份证和医保卡',
            date=start_time.date()
        )

        self.assertEqual(event.user, self.user)
        self.assertEqual(event.title, '产检预约')
        self.assertEqual(event.location, '妇幼保健院')
        self.assertEqual(event.reminder_minutes, 60)
        self.assertEqual(str(event), '产检预约')

    def test_event_time_validation(self):
        """测试事项时间验证"""
        start_time = timezone.now() + datetime.timedelta(days=1)
        end_time = start_time - datetime.timedelta(hours=1)  # 错误：结束时间早于开始时间

        event = Event(
            user=self.user,
            title='测试事项',
            start_time=start_time,
            end_time=end_time,
            date=start_time.date()
        )

        with self.assertRaises(ValidationError):
            event.clean()

    def test_date_consistency_validation(self):
        """测试日期一致性验证"""
        start_time = timezone.now() + datetime.timedelta(days=1)
        wrong_date = start_time.date() + datetime.timedelta(days=1)  # 错误：日期不一致

        event = Event(
            user=self.user,
            title='测试事项',
            start_time=start_time,
            end_time=start_time + datetime.timedelta(hours=1),
            date=wrong_date
        )

        with self.assertRaises(ValidationError):
            event.clean()

    def test_negative_reminder_validation(self):
        """测试负数提醒时间验证"""
        start_time = timezone.now() + datetime.timedelta(days=1)

        event = Event(
            user=self.user,
            title='测试事项',
            start_time=start_time,
            end_time=start_time + datetime.timedelta(hours=1),
            date=start_time.date(),
            reminder_minutes=-10  # 错误：负数提醒时间
        )

        with self.assertRaises(ValidationError):
            event.clean()

    def test_event_status_properties(self):
        """测试事项状态属性"""
        now = timezone.now()

        # 即将到来的事项
        future_start = now + datetime.timedelta(hours=2)
        future_end = future_start + datetime.timedelta(hours=1)
        upcoming_event = Event.objects.create(
            user=self.user,
            title='即将到来的事项',
            start_time=future_start,
            end_time=future_end,
            date=future_start.date()
        )
        self.assertTrue(upcoming_event.is_upcoming)
        self.assertFalse(upcoming_event.is_past)
        self.assertFalse(upcoming_event.is_ongoing)

        # 正在进行的事项
        ongoing_start = now - datetime.timedelta(minutes=30)
        ongoing_end = now + datetime.timedelta(minutes=30)
        ongoing_event = Event.objects.create(
            user=self.user,
            title='正在进行的事项',
            start_time=ongoing_start,
            end_time=ongoing_end,
            date=ongoing_start.date()
        )
        self.assertFalse(ongoing_event.is_upcoming)
        self.assertFalse(ongoing_event.is_past)
        self.assertTrue(ongoing_event.is_ongoing)

        # 已过期的事项
        past_start = now - datetime.timedelta(hours=2)
        past_end = now - datetime.timedelta(hours=1)
        past_event = Event.objects.create(
            user=self.user,
            title='已过期的事项',
            start_time=past_start,
            end_time=past_end,
            date=past_start.date()
        )
        self.assertFalse(past_event.is_upcoming)
        self.assertTrue(past_event.is_past)
        self.assertFalse(past_event.is_ongoing)

    def test_duration_property(self):
        """测试持续时间属性"""
        start_time = timezone.now() + datetime.timedelta(days=1)
        end_time = start_time + datetime.timedelta(hours=2, minutes=30)

        event = Event.objects.create(
            user=self.user,
            title='测试事项',
            start_time=start_time,
            end_time=end_time,
            date=start_time.date()
        )

        expected_duration = datetime.timedelta(hours=2, minutes=30)
        self.assertEqual(event.duration, expected_duration)

    def test_get_reminder_time(self):
        """测试获取提醒时间"""
        start_time = timezone.now() + datetime.timedelta(days=1)

        event = Event.objects.create(
            user=self.user,
            title='测试事项',
            start_time=start_time,
            end_time=start_time + datetime.timedelta(hours=1),
            date=start_time.date(),
            reminder_minutes=30
        )

        expected_reminder = start_time - datetime.timedelta(minutes=30)
        self.assertEqual(event.get_reminder_time(), expected_reminder)

    def test_get_departure_time(self):
        """测试获取出发时间"""
        start_time = timezone.now() + datetime.timedelta(days=1)
        travel_duration = datetime.timedelta(minutes=45)

        event = Event.objects.create(
            user=self.user,
            title='测试事项',
            start_time=start_time,
            end_time=start_time + datetime.timedelta(hours=1),
            date=start_time.date(),
            travel_duration=travel_duration
        )

        expected_departure = start_time - travel_duration
        self.assertEqual(event.get_departure_time(), expected_departure)

    def test_optional_fields(self):
        """测试可选字段"""
        start_time = timezone.now() + datetime.timedelta(days=1)

        # 只包含必填字段的事项
        event = Event.objects.create(
            user=self.user,
            title='简单事项',
            start_time=start_time,
            end_time=start_time + datetime.timedelta(hours=1),
            date=start_time.date()
        )

        self.assertEqual(event.location, '')
        self.assertIsNone(event.travel_duration)
        self.assertIsNone(event.reminder_minutes)
        self.assertEqual(event.notes, '')
        self.assertIsNone(event.get_reminder_time())
        self.assertIsNone(event.get_departure_time())


class TaskModelTest(TestCase):
    """任务模型测试"""

    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_custom_task(self):
        """测试创建自定义任务"""
        start_time = timezone.now() + datetime.timedelta(days=1, hours=2)
        end_time = start_time + datetime.timedelta(hours=1)

        task = Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='重要会议',
            is_all_day=False,
            start_time=start_time,
            end_time=end_time,
            location='会议室A',
            travel_time=datetime.timedelta(minutes=15),
            reminder_settings=[
                {'minutes': 15, 'enabled': True},
                {'minutes': 60, 'enabled': False}
            ],
            notes='准备会议材料'
        )

        self.assertEqual(task.user, self.user)
        self.assertEqual(task.task_type, Task.TaskType.CUSTOM)
        self.assertEqual(task.title, '重要会议')
        self.assertFalse(task.is_all_day)
        self.assertEqual(task.location, '会议室A')
        self.assertEqual(str(task), '重要会议 (自定义任务)')

    def test_create_medication_task(self):
        """测试创建吃药提醒任务"""
        task = Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.MEDICATION,
            medicine_name='维生素D',
            dosage_amount=Decimal('2.00'),
            dosage_unit=Task.DosageUnit.TABLET,
            medication_time=datetime.time(8, 0),
            reminder_advance_time=datetime.timedelta(minutes=10),
            weekly_schedule=[1, 2, 3, 4, 5]  # 工作日
        )

        self.assertEqual(task.user, self.user)
        self.assertEqual(task.task_type, Task.TaskType.MEDICATION)
        self.assertEqual(task.medicine_name, '维生素D')
        self.assertEqual(task.dosage_amount, Decimal('2.00'))
        self.assertEqual(task.dosage_unit, Task.DosageUnit.TABLET)
        self.assertEqual(task.weekly_schedule, [1, 2, 3, 4, 5])
        self.assertEqual(str(task), '维生素D (吃药提醒)')

    def test_custom_task_validation(self):
        """测试自定义任务验证"""
        # 测试缺少标题
        task = Task(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            # title 缺失
            is_all_day=False
        )
        with self.assertRaises(ValidationError):
            task.clean()

        # 测试非全天任务缺少时间
        task = Task(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='测试任务',
            is_all_day=False
            # start_time 和 end_time 缺失
        )
        with self.assertRaises(ValidationError):
            task.clean()

        # 测试结束时间早于开始时间
        start_time = timezone.now() + datetime.timedelta(days=1)
        end_time = start_time - datetime.timedelta(hours=1)
        task = Task(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='测试任务',
            is_all_day=False,
            start_time=start_time,
            end_time=end_time
        )
        with self.assertRaises(ValidationError):
            task.clean()

    def test_medication_task_validation(self):
        """测试吃药提醒任务验证"""
        # 测试缺少药物名称
        task = Task(
            user=self.user,
            task_type=Task.TaskType.MEDICATION,
            # medicine_name 缺失
            medication_time=datetime.time(8, 0),
            weekly_schedule=[1, 2, 3]
        )
        with self.assertRaises(ValidationError):
            task.clean()

        # 测试缺少服药时间
        task = Task(
            user=self.user,
            task_type=Task.TaskType.MEDICATION,
            medicine_name='测试药物',
            # medication_time 缺失
            weekly_schedule=[1, 2, 3]
        )
        with self.assertRaises(ValidationError):
            task.clean()

        # 测试缺少周期设置
        task = Task(
            user=self.user,
            task_type=Task.TaskType.MEDICATION,
            medicine_name='测试药物',
            medication_time=datetime.time(8, 0)
            # weekly_schedule 缺失
        )
        with self.assertRaises(ValidationError):
            task.clean()

        # 测试无效的周期设置
        task = Task(
            user=self.user,
            task_type=Task.TaskType.MEDICATION,
            medicine_name='测试药物',
            medication_time=datetime.time(8, 0),
            weekly_schedule=[0, 8, 9]  # 无效的星期数
        )
        with self.assertRaises(ValidationError):
            task.clean()

    def test_reminder_settings_validation(self):
        """测试提醒设置验证"""
        start_time = timezone.now() + datetime.timedelta(days=1)
        end_time = start_time + datetime.timedelta(hours=1)

        # 测试无效的提醒设置格式
        task = Task(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='测试任务',
            start_time=start_time,
            end_time=end_time,
            reminder_settings="invalid"  # 应该是列表
        )
        with self.assertRaises(ValidationError):
            task.clean()

        # 测试无效的提醒设置项
        task = Task(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='测试任务',
            start_time=start_time,
            end_time=end_time,
            reminder_settings=[
                {'minutes': -10, 'enabled': True}  # 负数分钟
            ]
        )
        with self.assertRaises(ValidationError):
            task.clean()

    def test_task_status_properties(self):
        """测试任务状态属性"""
        now = timezone.now()

        # 测试自定义任务状态
        future_start = now + datetime.timedelta(hours=2)
        future_end = future_start + datetime.timedelta(hours=1)
        custom_task = Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='未来任务',
            start_time=future_start,
            end_time=future_end
        )
        self.assertTrue(custom_task.is_upcoming)
        self.assertFalse(custom_task.is_past)
        self.assertFalse(custom_task.is_ongoing)

        # 测试吃药提醒状态（今天在周期内）
        today_weekday = now.date().isoweekday()
        future_time = (now + datetime.timedelta(hours=1)).time()
        medication_task = Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.MEDICATION,
            medicine_name='测试药物',
            medication_time=future_time,
            weekly_schedule=[today_weekday]
        )
        self.assertTrue(medication_task.is_upcoming)

    def test_weekly_schedule_display(self):
        """测试周期设置显示"""
        task = Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.MEDICATION,
            medicine_name='测试药物',
            medication_time=datetime.time(8, 0),
            weekly_schedule=[1, 2, 3, 4, 5, 6, 7]
        )
        self.assertEqual(task.get_weekly_schedule_display(), '每天')

        task.weekly_schedule = [1, 2, 3, 4, 5]
        task.save()
        self.assertEqual(task.get_weekly_schedule_display(), '工作日')

        task.weekly_schedule = [6, 7]
        task.save()
        self.assertEqual(task.get_weekly_schedule_display(), '周末')

        task.weekly_schedule = [1, 3, 5]
        task.save()
        self.assertEqual(task.get_weekly_schedule_display(), '周一、周三、周五')

    def test_dosage_display(self):
        """测试药物剂量显示"""
        task = Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.MEDICATION,
            medicine_name='测试药物',
            dosage_amount=Decimal('2.50'),
            dosage_unit=Task.DosageUnit.ML,
            medication_time=datetime.time(8, 0),
            weekly_schedule=[1, 2, 3]
        )
        self.assertEqual(task.get_dosage_display(), '2.50毫升')

    def test_duration_property(self):
        """测试任务持续时间"""
        start_time = timezone.now() + datetime.timedelta(days=1)
        end_time = start_time + datetime.timedelta(hours=2, minutes=30)

        task = Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='测试任务',
            start_time=start_time,
            end_time=end_time
        )

        expected_duration = datetime.timedelta(hours=2, minutes=30)
        self.assertEqual(task.duration, expected_duration)

    def test_get_departure_time(self):
        """测试获取出发时间"""
        start_time = timezone.now() + datetime.timedelta(days=1)
        travel_time = datetime.timedelta(minutes=30)

        task = Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='测试任务',
            start_time=start_time,
            end_time=start_time + datetime.timedelta(hours=1),
            travel_time=travel_time
        )

        expected_departure = start_time - travel_time
        self.assertEqual(task.get_departure_time(), expected_departure)


class TaskAPITest(APITestCase):
    """任务API测试"""

    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

    def test_create_custom_task(self):
        """测试创建自定义任务"""
        start_time = timezone.now() + datetime.timedelta(days=1, hours=2)
        end_time = start_time + datetime.timedelta(hours=1)

        data = {
            'task_type': Task.TaskType.CUSTOM,
            'title': '重要会议',
            'is_all_day': False,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'location': '会议室A',
            'travel_time': '00:15:00',  # 15分钟
            'reminder_settings': [
                {'minutes': 15, 'enabled': True},
                {'minutes': 60, 'enabled': False}
            ],
            'notes': '准备会议材料'
        }

        response = self.client.post('/api/calendar/tasks/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['title'], '重要会议')
        self.assertEqual(response.data['task_type'], Task.TaskType.CUSTOM)

    def test_create_medication_task(self):
        """测试创建吃药提醒任务"""
        data = {
            'task_type': Task.TaskType.MEDICATION,
            'medicine_name': '维生素D',
            'dosage_amount': '2.00',
            'dosage_unit': Task.DosageUnit.TABLET,
            'medication_time': '08:00:00',
            'reminder_advance_time': '00:10:00',  # 10分钟
            'weekly_schedule': [1, 2, 3, 4, 5]  # 工作日
        }

        response = self.client.post('/api/calendar/tasks/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['medicine_name'], '维生素D')
        self.assertEqual(response.data['task_type'], Task.TaskType.MEDICATION)
        self.assertEqual(response.data['weekly_schedule'], [1, 2, 3, 4, 5])

    def test_create_task_validation_error(self):
        """测试创建任务时的验证错误"""
        # 自定义任务缺少标题
        data = {
            'task_type': Task.TaskType.CUSTOM,
            'is_all_day': False,
            'start_time': (timezone.now() + datetime.timedelta(days=1)).isoformat(),
            'end_time': (timezone.now() + datetime.timedelta(days=1, hours=1)).isoformat()
        }

        response = self.client.post('/api/calendar/tasks/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('title', response.data)

    def test_list_tasks(self):
        """测试获取任务列表"""
        # 创建测试任务
        Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='测试任务1',
            start_time=timezone.now() + datetime.timedelta(days=1),
            end_time=timezone.now() + datetime.timedelta(days=1, hours=1)
        )
        Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.MEDICATION,
            medicine_name='测试药物',
            medication_time=datetime.time(8, 0),
            weekly_schedule=[1, 2, 3]
        )

        response = self.client.get('/api/calendar/tasks/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)

    def test_filter_tasks_by_type(self):
        """测试按类型筛选任务"""
        # 创建不同类型的任务
        Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='自定义任务',
            start_time=timezone.now() + datetime.timedelta(days=1),
            end_time=timezone.now() + datetime.timedelta(days=1, hours=1)
        )
        Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.MEDICATION,
            medicine_name='测试药物',
            medication_time=datetime.time(8, 0),
            weekly_schedule=[1, 2, 3]
        )

        # 筛选自定义任务
        response = self.client.get('/api/calendar/tasks/', {'task_type': Task.TaskType.CUSTOM})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['task_type'], Task.TaskType.CUSTOM)

        # 筛选吃药提醒
        response = self.client.get('/api/calendar/tasks/', {'task_type': Task.TaskType.MEDICATION})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['task_type'], Task.TaskType.MEDICATION)

    def test_get_upcoming_tasks(self):
        """测试获取即将到来的任务"""
        # 创建未来的任务
        future_start = timezone.now() + datetime.timedelta(days=1, hours=2)
        Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='未来任务',
            start_time=future_start,
            end_time=future_start + datetime.timedelta(hours=1)
        )

        # 创建过去的任务
        past_start = timezone.now() - datetime.timedelta(days=1)
        Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='过去任务',
            start_time=past_start,
            end_time=past_start + datetime.timedelta(hours=1)
        )

        response = self.client.get('/api/calendar/tasks/upcoming/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['title'], '未来任务')

    def test_get_today_medications(self):
        """测试获取今天的吃药提醒"""
        today_weekday = timezone.now().date().isoweekday()

        # 创建今天在周期内的吃药提醒
        Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.MEDICATION,
            medicine_name='今天的药物',
            medication_time=datetime.time(8, 0),
            weekly_schedule=[today_weekday]
        )

        # 创建今天不在周期内的吃药提醒
        other_weekday = 1 if today_weekday != 1 else 2
        Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.MEDICATION,
            medicine_name='其他天的药物',
            medication_time=datetime.time(9, 0),
            weekly_schedule=[other_weekday]
        )

        response = self.client.get('/api/calendar/tasks/today_medications/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['medicine_name'], '今天的药物')

    def test_update_task(self):
        """测试更新任务"""
        task = Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='原标题',
            start_time=timezone.now() + datetime.timedelta(days=1),
            end_time=timezone.now() + datetime.timedelta(days=1, hours=1)
        )

        data = {
            'title': '新标题',
            'notes': '更新的备注'
        }

        response = self.client.patch(f'/api/calendar/tasks/{task.id}/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], '新标题')
        self.assertEqual(response.data['notes'], '更新的备注')

    def test_delete_task(self):
        """测试删除任务"""
        task = Task.objects.create(
            user=self.user,
            task_type=Task.TaskType.CUSTOM,
            title='待删除任务',
            start_time=timezone.now() + datetime.timedelta(days=1),
            end_time=timezone.now() + datetime.timedelta(days=1, hours=1)
        )

        response = self.client.delete(f'/api/calendar/tasks/{task.id}/')
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(Task.objects.filter(id=task.id).exists())

    def test_permission_required(self):
        """测试需要认证权限"""
        self.client.force_authenticate(user=None)  # 取消认证

        response = self.client.get('/api/calendar/tasks/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_user_isolation(self):
        """测试用户数据隔离"""
        # 创建另一个用户和任务
        other_user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        Task.objects.create(
            user=other_user,
            task_type=Task.TaskType.CUSTOM,
            title='其他用户的任务',
            start_time=timezone.now() + datetime.timedelta(days=1),
            end_time=timezone.now() + datetime.timedelta(days=1, hours=1)
        )

        # 当前用户不应该看到其他用户的任务
        response = self.client.get('/api/calendar/tasks/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 0)
