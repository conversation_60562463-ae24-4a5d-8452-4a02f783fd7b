from rest_framework import serializers
from django.core.files.uploadedfile import InMemoryUploadedFile, TemporaryUploadedFile
from .image_models import PrenatalImage, ImageUploadSession
from .models import PrenatalCheckup


class PrenatalImageSerializer(serializers.ModelSerializer):
    """产检图片序列化器"""
    
    user_email = serializers.CharField(source='user.email', read_only=True)
    file_size_human = serializers.ReadOnlyField()
    image_url = serializers.ReadOnlyField()
    thumbnail_url = serializers.ReadOnlyField()
    checkup_datetime = serializers.DateTimeField(source='checkup.datetime', read_only=True)
    checkup_location = serializers.CharField(source='checkup.location', read_only=True)
    
    class Meta:
        model = PrenatalImage
        fields = [
            'id', 'user', 'user_email', 'image', 'image_type', 'title', 'description',
            'file_size', 'file_size_human', 'width', 'height', 'image_url', 'thumbnail_url',
            'checkup', 'checkup_datetime', 'checkup_location',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user_email', 'file_size', 'file_size_human', 'width', 'height',
            'image_url', 'thumbnail_url', 'checkup_datetime', 'checkup_location',
            'created_at', 'updated_at'
        ]
    
    def create(self, validated_data):
        """创建图片时自动设置用户"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)
    
    def validate_image(self, value):
        """验证图片文件"""
        if not value:
            raise serializers.ValidationError('请选择图片文件')
        
        # 检查文件大小（限制为10MB）
        max_size = 10 * 1024 * 1024  # 10MB
        if value.size > max_size:
            raise serializers.ValidationError('图片文件大小不能超过10MB')
        
        # 检查文件类型
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp']
        if hasattr(value, 'content_type') and value.content_type not in allowed_types:
            raise serializers.ValidationError('不支持的图片格式，请上传 jpg, png, gif, bmp 或 webp 格式的图片')
        
        return value
    
    def validate_checkup(self, value):
        """验证产检记录归属"""
        if value and value.user != self.context['request'].user:
            raise serializers.ValidationError('只能关联自己的产检记录')
        return value


class PrenatalImageUploadSerializer(serializers.ModelSerializer):
    """产检图片上传序列化器（简化版）"""
    
    class Meta:
        model = PrenatalImage
        fields = ['image', 'image_type', 'title', 'description', 'checkup']
    
    def create(self, validated_data):
        """创建图片时自动设置用户"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class PrenatalImageListSerializer(serializers.ModelSerializer):
    """产检图片列表序列化器（简化版）"""
    
    file_size_human = serializers.ReadOnlyField()
    image_url = serializers.ReadOnlyField()
    thumbnail_url = serializers.ReadOnlyField()
    
    class Meta:
        model = PrenatalImage
        fields = [
            'id', 'image_type', 'title', 'file_size_human', 
            'image_url', 'thumbnail_url', 'created_at'
        ]


class ImageUploadSessionSerializer(serializers.ModelSerializer):
    """图片上传会话序列化器"""
    
    user_email = serializers.CharField(source='user.email', read_only=True)
    progress_percentage = serializers.ReadOnlyField()
    is_completed = serializers.ReadOnlyField()
    checkup_datetime = serializers.DateTimeField(source='checkup.datetime', read_only=True)
    
    class Meta:
        model = ImageUploadSession
        fields = [
            'id', 'user', 'user_email', 'session_name', 'total_files', 
            'uploaded_files', 'failed_files', 'status', 'progress_percentage',
            'is_completed', 'checkup', 'checkup_datetime',
            'created_at', 'updated_at', 'completed_at'
        ]
        read_only_fields = [
            'id', 'user_email', 'uploaded_files', 'failed_files', 
            'progress_percentage', 'is_completed', 'checkup_datetime',
            'created_at', 'updated_at', 'completed_at'
        ]
    
    def create(self, validated_data):
        """创建上传会话时自动设置用户"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class BatchImageUploadSerializer(serializers.Serializer):
    """批量图片上传序列化器"""
    
    images = serializers.ListField(
        child=serializers.ImageField(),
        min_length=1,
        max_length=20,  # 限制一次最多上传20张图片
        help_text='图片文件列表，最多20张'
    )
    image_type = serializers.ChoiceField(
        choices=PrenatalImage.IMAGE_TYPE_CHOICES,
        default='other',
        help_text='图片类型'
    )
    checkup = serializers.PrimaryKeyRelatedField(
        queryset=PrenatalCheckup.objects.none(),
        required=False,
        allow_null=True,
        help_text='关联的产检记录ID'
    )
    session_name = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        help_text='上传会话名称'
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 动态设置产检记录查询集为当前用户的记录
        if 'context' in kwargs and 'request' in kwargs['context']:
            user = kwargs['context']['request'].user
            self.fields['checkup'].queryset = PrenatalCheckup.objects.filter(user=user)
    
    def validate_images(self, value):
        """验证图片列表"""
        max_size = 10 * 1024 * 1024  # 10MB per image
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp']
        
        for image in value:
            # 检查文件大小
            if image.size > max_size:
                raise serializers.ValidationError(f'图片 {image.name} 大小超过10MB限制')
            
            # 检查文件类型
            if hasattr(image, 'content_type') and image.content_type not in allowed_types:
                raise serializers.ValidationError(f'图片 {image.name} 格式不支持')
        
        return value
    
    def create(self, validated_data):
        """批量创建图片"""
        images = validated_data.pop('images')
        user = self.context['request'].user
        
        # 创建上传会话
        session = ImageUploadSession.objects.create(
            user=user,
            session_name=validated_data.get('session_name', ''),
            total_files=len(images),
            checkup=validated_data.get('checkup'),
            status='uploading'
        )
        
        created_images = []
        failed_count = 0
        
        for i, image in enumerate(images):
            try:
                # 创建图片记录
                prenatal_image = PrenatalImage.objects.create(
                    user=user,
                    image=image,
                    image_type=validated_data.get('image_type', 'other'),
                    title=f'图片 {i+1}',
                    checkup=validated_data.get('checkup')
                )
                created_images.append(prenatal_image)
                session.increment_uploaded()
            except Exception as e:
                failed_count += 1
                session.increment_failed()
        
        # 如果有失败的文件，更新会话状态
        if failed_count > 0 and failed_count == len(images):
            session.status = 'failed'
            session.save(update_fields=['status'])
        
        return {
            'session': session,
            'created_images': created_images,
            'success_count': len(created_images),
            'failed_count': failed_count
        }
