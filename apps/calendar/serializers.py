from rest_framework import serializers
from .models import PrenatalItem, CustomPrenatalItem, PrenatalCheckup, Task


class PrenatalItemSerializer(serializers.ModelSerializer):
    """标准产检项目序列化器"""
    
    week_range = serializers.SerializerMethodField()
    
    class Meta:
        model = PrenatalItem
        fields = [
            'id', 'name', 'content', 'start_week', 'end_week', 
            'week_range', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'week_range']
    
    def get_week_range(self, obj):
        """获取孕周范围描述"""
        if obj.start_week and obj.end_week:
            return f'{obj.start_week}-{obj.end_week}周'
        elif obj.start_week:
            return f'{obj.start_week}周+'
        elif obj.end_week:
            return f'≤{obj.end_week}周'
        return '不限'


class CustomPrenatalItemSerializer(serializers.ModelSerializer):
    """自定义产检项目序列化器"""
    
    created_by_email = serializers.CharField(source='created_by.email', read_only=True)
    week_range = serializers.SerializerMethodField()
    
    class Meta:
        model = CustomPrenatalItem
        fields = [
            'id', 'name', 'content', 'start_week', 'end_week', 
            'created_by', 'created_by_email', 'week_range', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by_email', 'week_range', 'created_at', 'updated_at']
    
    def get_week_range(self, obj):
        """获取孕周范围描述"""
        if obj.start_week and obj.end_week:
            return f'{obj.start_week}-{obj.end_week}周'
        elif obj.start_week:
            return f'{obj.start_week}周+'
        elif obj.end_week:
            return f'≤{obj.end_week}周'
        return '不限'
    
    def create(self, validated_data):
        """创建自定义产检项目时自动设置创建者"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class PrenatalCheckupSerializer(serializers.ModelSerializer):
    """产检记录序列化器"""
    
    user_email = serializers.CharField(source='user.email', read_only=True)
    prenatal_items_detail = PrenatalItemSerializer(source='prenatal_items', many=True, read_only=True)
    custom_prenatal_items_detail = CustomPrenatalItemSerializer(source='custom_prenatal_items', many=True, read_only=True)
    items_count = serializers.SerializerMethodField()
    is_upcoming = serializers.ReadOnlyField()
    is_past_due = serializers.ReadOnlyField()
    
    class Meta:
        model = PrenatalCheckup
        fields = [
            'id', 'user', 'user_email', 'date', 'time', 'location',
            'prenatal_items', 'custom_prenatal_items',
            'prenatal_items_detail', 'custom_prenatal_items_detail',
            'travel_time', 'reminder_time', 'preparation_notes',
            'checkup_notes', 'checkup_images',
            'items_count', 'is_upcoming', 'is_past_due',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user_email', 'prenatal_items_detail', 
            'custom_prenatal_items_detail', 'items_count', 
            'is_upcoming', 'is_past_due', 'created_at', 'updated_at'
        ]
    
    def get_items_count(self, obj):
        """获取产检项目总数"""
        standard_count = obj.prenatal_items.count()
        custom_count = obj.custom_prenatal_items.count()
        return {
            'standard': standard_count,
            'custom': custom_count,
            'total': standard_count + custom_count
        }
    
    def create(self, validated_data):
        """创建产检记录时自动设置用户"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)
    
    def validate(self, data):
        """验证整体数据"""
        from django.utils import timezone
        from datetime import datetime

        # 验证产检时间不能是过去时间（创建时）
        if not self.instance and data.get('date') and data.get('time'):
            checkup_datetime = datetime.combine(data['date'], data['time'])
            if timezone.make_aware(checkup_datetime) < timezone.now():
                raise serializers.ValidationError({
                    'date': '产检时间不能是过去时间',
                    'time': '产检时间不能是过去时间'
                })

        # 验证提醒时间和出行时间的关系
        reminder_time = data.get('reminder_time')
        travel_time = data.get('travel_time')

        if reminder_time and travel_time and reminder_time > travel_time:
            raise serializers.ValidationError({
                'reminder_time': '提醒时间应该在出行时间之前'
            })

        return data



class PrenatalCheckupListSerializer(serializers.ModelSerializer):
    """产检记录列表序列化器（简化版）"""
    
    items_count = serializers.SerializerMethodField()
    is_upcoming = serializers.ReadOnlyField()
    
    class Meta:
        model = PrenatalCheckup
        fields = [
            'id', 'date', 'time', 'location',
            'items_count', 'is_upcoming', 'created_at'
        ]
    
    def get_items_count(self, obj):
        """获取产检项目总数"""
        return obj.prenatal_items.count() + obj.custom_prenatal_items.count()


class TaskSerializer(serializers.ModelSerializer):
    """任务序列化器 - 支持自定义任务和吃药提醒"""

    user_email = serializers.CharField(source='user.email', read_only=True)
    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    dosage_unit_display = serializers.CharField(source='get_dosage_unit_display', read_only=True)
    weekly_schedule_display = serializers.SerializerMethodField()
    dosage_display = serializers.SerializerMethodField()
    is_upcoming = serializers.ReadOnlyField()
    is_past = serializers.ReadOnlyField()
    is_ongoing = serializers.ReadOnlyField()
    duration = serializers.ReadOnlyField()
    next_reminder_time = serializers.SerializerMethodField()
    departure_time = serializers.SerializerMethodField()

    class Meta:
        model = Task
        fields = [
            'id', 'user', 'user_email', 'task_type', 'task_type_display',
            # 自定义任务字段
            'title', 'is_all_day', 'start_time', 'end_time', 'location',
            'travel_time', 'reminder_settings', 'notes',
            # 吃药提醒字段
            'medicine_name', 'dosage_amount', 'dosage_unit', 'dosage_unit_display',
            'medication_time', 'reminder_advance_time', 'weekly_schedule',
            # 计算字段
            'weekly_schedule_display', 'dosage_display', 'is_upcoming', 'is_past',
            'is_ongoing', 'duration', 'next_reminder_time', 'departure_time',
            # 时间戳
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user', 'user_email', 'task_type_display', 'dosage_unit_display',
            'weekly_schedule_display', 'dosage_display', 'is_upcoming', 'is_past',
            'is_ongoing', 'duration', 'next_reminder_time', 'departure_time',
            'created_at', 'updated_at'
        ]

    def get_weekly_schedule_display(self, obj):
        """获取周期设置的显示文本"""
        return obj.get_weekly_schedule_display()

    def get_dosage_display(self, obj):
        """获取药物剂量的显示文本"""
        return obj.get_dosage_display()

    def get_next_reminder_time(self, obj):
        """获取下次提醒时间"""
        reminder_time = obj.get_next_reminder_time()
        return reminder_time.isoformat() if reminder_time else None

    def get_departure_time(self, obj):
        """获取出发时间"""
        departure_time = obj.get_departure_time()
        return departure_time.isoformat() if departure_time else None

    def create(self, validated_data):
        """创建任务时自动设置用户"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)

    def validate(self, data):
        """验证整体数据"""
        task_type = data.get('task_type')

        # 自定义任务验证
        if task_type == Task.TaskType.CUSTOM:
            if not data.get('title'):
                raise serializers.ValidationError({
                    'title': '自定义任务必须填写标题'
                })

            if not data.get('is_all_day', False):
                if not data.get('start_time'):
                    raise serializers.ValidationError({
                        'start_time': '非全天任务必须设置开始时间'
                    })
                if not data.get('end_time'):
                    raise serializers.ValidationError({
                        'end_time': '非全天任务必须设置结束时间'
                    })

                start_time = data.get('start_time')
                end_time = data.get('end_time')
                if start_time and end_time and start_time >= end_time:
                    raise serializers.ValidationError({
                        'end_time': '结束时间必须晚于开始时间'
                    })

        # 吃药提醒验证
        elif task_type == Task.TaskType.MEDICATION:
            if not data.get('medicine_name'):
                raise serializers.ValidationError({
                    'medicine_name': '吃药提醒必须填写药物名称'
                })
            if not data.get('medication_time'):
                raise serializers.ValidationError({
                    'medication_time': '吃药提醒必须设置服药时间'
                })

            dosage_amount = data.get('dosage_amount')
            if dosage_amount is not None and dosage_amount <= 0:
                raise serializers.ValidationError({
                    'dosage_amount': '药物数量必须大于0'
                })

            weekly_schedule = data.get('weekly_schedule')
            if not weekly_schedule:
                raise serializers.ValidationError({
                    'weekly_schedule': '吃药提醒必须设置周期'
                })

            # 验证周期设置格式
            if not isinstance(weekly_schedule, list):
                raise serializers.ValidationError({
                    'weekly_schedule': '周期设置必须是列表格式'
                })

            for day in weekly_schedule:
                if not isinstance(day, int) or day < 1 or day > 7:
                    raise serializers.ValidationError({
                        'weekly_schedule': '周期设置必须是1-7的整数列表（1=周一，7=周日）'
                    })

        # 验证提醒设置格式
        reminder_settings = data.get('reminder_settings')
        if reminder_settings:
            if not isinstance(reminder_settings, list):
                raise serializers.ValidationError({
                    'reminder_settings': '提醒设置必须是列表格式'
                })

            for i, reminder in enumerate(reminder_settings):
                if not isinstance(reminder, dict):
                    raise serializers.ValidationError({
                        'reminder_settings': f'提醒设置项{i+1}必须是字典格式'
                    })

                if 'minutes' not in reminder or 'enabled' not in reminder:
                    raise serializers.ValidationError({
                        'reminder_settings': f'提醒设置项{i+1}必须包含minutes和enabled字段'
                    })

                if not isinstance(reminder['minutes'], int) or reminder['minutes'] < 0:
                    raise serializers.ValidationError({
                        'reminder_settings': f'提醒设置项{i+1}的minutes必须是非负整数'
                    })

                if not isinstance(reminder['enabled'], bool):
                    raise serializers.ValidationError({
                        'reminder_settings': f'提醒设置项{i+1}的enabled必须是布尔值'
                    })

        return data


class TaskListSerializer(serializers.ModelSerializer):
    """任务列表序列化器（简化版）"""

    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    weekly_schedule_display = serializers.SerializerMethodField()
    dosage_display = serializers.SerializerMethodField()
    is_upcoming = serializers.ReadOnlyField()

    class Meta:
        model = Task
        fields = [
            'id', 'task_type', 'task_type_display', 'title', 'medicine_name',
            'start_time', 'medication_time', 'weekly_schedule_display',
            'dosage_display', 'is_upcoming', 'created_at'
        ]

    def get_weekly_schedule_display(self, obj):
        """获取周期设置的显示文本"""
        return obj.get_weekly_schedule_display()

    def get_dosage_display(self, obj):
        """获取药物剂量的显示文本"""
        return obj.get_dosage_display()
