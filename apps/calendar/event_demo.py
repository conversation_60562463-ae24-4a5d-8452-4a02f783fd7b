#!/usr/bin/env python
"""
Event 模型使用演示脚本

这个脚本演示了如何使用新创建的 Event 模型来管理事项/事件。
运行方式：uv run python manage.py shell < apps/calendar/event_demo.py
"""

from django.utils import timezone
from datetime import datetime, timedelta
from apps.users.models import UserAccount
from apps.calendar.models import Event

# 创建或获取测试用户
user, created = UserAccount.objects.get_or_create(
    email='<EMAIL>',
    defaults={
        'password': 'demo123456'
    }
)

if created:
    print(f"创建了新用户: {user.email}")
else:
    print(f"使用现有用户: {user.email}")

# 创建一个产检预约事项
tomorrow = timezone.now() + timedelta(days=1)
checkup_start = tomorrow.replace(hour=9, minute=0, second=0, microsecond=0)
checkup_end = checkup_start + timedelta(hours=2)

checkup_event = Event.objects.create(
    user=user,
    title='产检预约 - 32周常规检查',
    start_time=checkup_start,
    end_time=checkup_end,
    location='市妇幼保健院产科门诊',
    travel_duration=timedelta(minutes=45),  # 45分钟车程
    reminder_minutes=120,  # 提前2小时提醒
    notes='记得带身份证、医保卡、孕妇手册。空腹检查，不要吃早餐。',
    date=checkup_start.date()
)

print(f"\n创建了产检事项: {checkup_event}")
print(f"开始时间: {checkup_event.start_time}")
print(f"结束时间: {checkup_event.end_time}")
print(f"持续时间: {checkup_event.duration}")
print(f"提醒时间: {checkup_event.get_reminder_time()}")
print(f"出发时间: {checkup_event.get_departure_time()}")
print(f"状态: {'即将开始' if checkup_event.is_upcoming else '其他'}")

# 创建一个孕期课程事项
next_week = timezone.now() + timedelta(days=7)
class_start = next_week.replace(hour=14, minute=0, second=0, microsecond=0)
class_end = class_start + timedelta(hours=1, minutes=30)

class_event = Event.objects.create(
    user=user,
    title='孕期瑜伽课程',
    start_time=class_start,
    end_time=class_end,
    location='健康中心3楼瑜伽室',
    travel_duration=timedelta(minutes=20),  # 20分钟车程
    reminder_minutes=60,  # 提前1小时提醒
    notes='穿舒适的运动服装，带瑜伽垫。',
    date=class_start.date()
)

print(f"\n创建了课程事项: {class_event}")
print(f"地点: {class_event.location}")
print(f"备注: {class_event.notes}")

# 创建一个购物提醒事项
shopping_start = timezone.now() + timedelta(days=3, hours=10)
shopping_end = shopping_start + timedelta(hours=2)

shopping_event = Event.objects.create(
    user=user,
    title='购买婴儿用品',
    start_time=shopping_start,
    end_time=shopping_end,
    location='母婴用品商场',
    travel_duration=timedelta(minutes=30),
    reminder_minutes=30,  # 提前30分钟提醒
    notes='购买清单：婴儿床、婴儿车、奶瓶、尿布、婴儿衣服',
    date=shopping_start.date()
)

print(f"\n创建了购物事项: {shopping_event}")

# 查询用户的所有事项
print(f"\n{user.email} 的所有事项:")
user_events = Event.objects.filter(user=user).order_by('start_time')
for event in user_events:
    status = "进行中" if event.is_ongoing else ("即将开始" if event.is_upcoming else "已结束")
    print(f"- {event.title} ({event.start_time.strftime('%Y-%m-%d %H:%M')}) - {status}")

print(f"\n总共创建了 {user_events.count()} 个事项")

# 演示数据验证
print("\n=== 数据验证演示 ===")
try:
    # 尝试创建一个结束时间早于开始时间的事项（应该失败）
    invalid_event = Event(
        user=user,
        title='无效事项',
        start_time=timezone.now() + timedelta(hours=2),
        end_time=timezone.now() + timedelta(hours=1),  # 结束时间早于开始时间
        date=(timezone.now() + timedelta(hours=2)).date()
    )
    invalid_event.clean()
    print("错误：应该抛出验证异常")
except Exception as e:
    print(f"正确：捕获到验证错误 - {e}")

print("\n演示完成！")
