# Generated by Django 5.2.4 on 2025-08-11 16:09

import apps.calendar.image_models
import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PrenatalItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='项目名称')),
                ('content', models.TextField(blank=True, help_text='详细描述产检项目的内容和注意事项', verbose_name='项目详细内容')),
                ('start_week', models.PositiveIntegerField(blank=True, help_text='适用的开始孕周（1-42周）', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(42)], verbose_name='适用开始孕周')),
                ('end_week', models.PositiveIntegerField(blank=True, help_text='适用的结束孕周（1-42周）', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(42)], verbose_name='适用结束孕周')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '标准产检项目',
                'verbose_name_plural': '标准产检项目',
                'db_table': 'prenatal_items',
                'ordering': ['start_week', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(help_text='事项的标题或名称', max_length=200, verbose_name='事项标题')),
                ('start_time', models.DateTimeField(help_text='事项的开始时间', verbose_name='开始时间')),
                ('end_time', models.DateTimeField(help_text='事项的结束时间', verbose_name='结束时间')),
                ('location', models.CharField(blank=True, help_text='事项发生的地点或位置', max_length=300, verbose_name='地点')),
                ('travel_duration', models.DurationField(blank=True, help_text='到达地点所需的出行时间（小时和分钟）', null=True, verbose_name='出行时间')),
                ('reminder_minutes', models.PositiveIntegerField(blank=True, help_text='提前多少分钟提醒（分钟数）', null=True, verbose_name='提醒时间')),
                ('notes', models.TextField(blank=True, help_text='事项的详细备注或说明', verbose_name='备注')),
                ('date', models.DateField(help_text='事项发生的日期', verbose_name='日期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '事项',
                'verbose_name_plural': '事项',
                'db_table': 'events',
                'ordering': ['date', 'start_time'],
            },
        ),
        migrations.CreateModel(
            name='ImageUploadSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_name', models.CharField(blank=True, max_length=100, verbose_name='会话名称')),
                ('total_files', models.PositiveIntegerField(default=0, verbose_name='总文件数')),
                ('uploaded_files', models.PositiveIntegerField(default=0, verbose_name='已上传文件数')),
                ('failed_files', models.PositiveIntegerField(default=0, verbose_name='失败文件数')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('uploading', '上传中'), ('completed', '已完成'), ('failed', '失败')], default='pending', max_length=20, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
            ],
            options={
                'verbose_name': '图片上传会话',
                'verbose_name_plural': '图片上传会话',
                'db_table': 'prenatal_image_upload_sessions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PrenatalCheckup',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('date', models.DateField(help_text='预约的产检日期', verbose_name='产检日期')),
                ('time', models.TimeField(help_text='预约的产检时间（小时和分钟）', verbose_name='产检时间')),
                ('location', models.CharField(help_text='医院或诊所名称及地址', max_length=200, verbose_name='产检地点')),
                ('travel_time', models.TimeField(blank=True, help_text='计划出发时间', null=True, verbose_name='出行时间')),
                ('reminder_time', models.TimeField(blank=True, help_text='提醒时间', null=True, verbose_name='提醒时间')),
                ('preparation_notes', models.TextField(blank=True, help_text='产检前的准备事项、注意事项等', verbose_name='产检准备笔记')),
                ('checkup_notes', models.TextField(blank=True, help_text='产检过程中的记录、医生建议等', verbose_name='产检笔记')),
                ('checkup_images', models.JSONField(blank=True, default=list, help_text='产检单、B超单等图片的URL列表', verbose_name='产检单图片')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '产检记录',
                'verbose_name_plural': '产检记录',
                'db_table': 'prenatal_checkups',
                'ordering': ['-date', '-time'],
            },
        ),
        migrations.CreateModel(
            name='PrenatalImage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('image', models.ImageField(help_text='支持的格式：jpg, jpeg, png, gif, bmp, webp', upload_to=apps.calendar.image_models.prenatal_image_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'])], verbose_name='图片文件')),
                ('image_type', models.CharField(choices=[('checkup_report', '产检报告'), ('ultrasound', 'B超单'), ('blood_test', '血检报告'), ('urine_test', '尿检报告'), ('other', '其他')], default='other', help_text='图片类型分类', max_length=20, verbose_name='图片类型')),
                ('title', models.CharField(blank=True, help_text='图片的简短描述', max_length=100, verbose_name='图片标题')),
                ('description', models.TextField(blank=True, help_text='详细描述图片内容', verbose_name='图片描述')),
                ('file_size', models.PositiveIntegerField(default=0, help_text='文件大小（字节）', verbose_name='文件大小')),
                ('width', models.PositiveIntegerField(default=0, help_text='图片宽度（像素）', verbose_name='图片宽度')),
                ('height', models.PositiveIntegerField(default=0, help_text='图片高度（像素）', verbose_name='图片高度')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '产检图片',
                'verbose_name_plural': '产检图片',
                'db_table': 'prenatal_images',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomPrenatalItem',
            fields=[
                ('prenatalitem_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='calendar.prenatalitem')),
            ],
            options={
                'verbose_name': '自定义产检项目',
                'verbose_name_plural': '自定义产检项目',
                'db_table': 'custom_prenatal_items',
                'ordering': ['created_by', 'prenatalitem_ptr__start_week', 'prenatalitem_ptr__name'],
            },
            bases=('calendar.prenatalitem',),
        ),
        migrations.AddIndex(
            model_name='prenatalitem',
            index=models.Index(fields=['start_week', 'end_week'], name='prenatal_it_start_w_43c885_idx'),
        ),
        migrations.AddIndex(
            model_name='prenatalitem',
            index=models.Index(fields=['name'], name='prenatal_it_name_b348b5_idx'),
        ),
        migrations.AddField(
            model_name='event',
            name='user',
            field=models.ForeignKey(help_text='创建该事项的用户', on_delete=django.db.models.deletion.CASCADE, related_name='events', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AddField(
            model_name='imageuploadsession',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='upload_sessions', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AddField(
            model_name='prenatalcheckup',
            name='prenatal_items',
            field=models.ManyToManyField(blank=True, help_text='选择的标准产检项目', related_name='standard_checkups', to='calendar.prenatalitem', verbose_name='标准产检项目'),
        ),
        migrations.AddField(
            model_name='prenatalcheckup',
            name='user',
            field=models.ForeignKey(help_text='进行产检的用户', on_delete=django.db.models.deletion.CASCADE, related_name='prenatal_checkups', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AddField(
            model_name='imageuploadsession',
            name='checkup',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='upload_sessions', to='calendar.prenatalcheckup', verbose_name='关联产检记录'),
        ),
        migrations.AddField(
            model_name='prenatalimage',
            name='checkup',
            field=models.ForeignKey(blank=True, help_text='可选：关联到具体的产检记录', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='image_files', to='calendar.prenatalcheckup', verbose_name='关联产检记录'),
        ),
        migrations.AddField(
            model_name='prenatalimage',
            name='user',
            field=models.ForeignKey(help_text='上传图片的用户', on_delete=django.db.models.deletion.CASCADE, related_name='prenatal_images', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AddField(
            model_name='prenatalcheckup',
            name='custom_prenatal_items',
            field=models.ManyToManyField(blank=True, help_text='选择的自定义产检项目', related_name='custom_checkups', to='calendar.customprenatalitem', verbose_name='自定义产检项目'),
        ),
        migrations.AddField(
            model_name='customprenatalitem',
            name='created_by',
            field=models.ForeignKey(help_text='创建该自定义项目的用户', on_delete=django.db.models.deletion.CASCADE, related_name='custom_prenatal_items', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['user', 'date', 'start_time'], name='events_user_id_765862_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['date', 'start_time'], name='events_date_0c6c56_idx'),
        ),
        migrations.AddIndex(
            model_name='event',
            index=models.Index(fields=['user', 'start_time'], name='events_user_id_893d7c_idx'),
        ),
        migrations.AddIndex(
            model_name='imageuploadsession',
            index=models.Index(fields=['user', 'status'], name='prenatal_im_user_id_bb33d5_idx'),
        ),
        migrations.AddIndex(
            model_name='imageuploadsession',
            index=models.Index(fields=['created_at'], name='prenatal_im_created_0c033b_idx'),
        ),
        migrations.AddIndex(
            model_name='prenatalimage',
            index=models.Index(fields=['user', 'created_at'], name='prenatal_im_user_id_45ff41_idx'),
        ),
        migrations.AddIndex(
            model_name='prenatalimage',
            index=models.Index(fields=['user', 'image_type'], name='prenatal_im_user_id_71dc1d_idx'),
        ),
        migrations.AddIndex(
            model_name='prenatalimage',
            index=models.Index(fields=['checkup'], name='prenatal_im_checkup_3b107d_idx'),
        ),
        migrations.AddIndex(
            model_name='prenatalimage',
            index=models.Index(fields=['created_at'], name='prenatal_im_created_30d319_idx'),
        ),
        migrations.AddIndex(
            model_name='prenatalcheckup',
            index=models.Index(fields=['user', 'date', 'time'], name='prenatal_ch_user_id_4e3dec_idx'),
        ),
        migrations.AddIndex(
            model_name='prenatalcheckup',
            index=models.Index(fields=['date', 'time'], name='prenatal_ch_date_2f8819_idx'),
        ),
        migrations.AddIndex(
            model_name='customprenatalitem',
            index=models.Index(fields=['created_by'], name='custom_pren_created_88a99b_idx'),
        ),
    ]
