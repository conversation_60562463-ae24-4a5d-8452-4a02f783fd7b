# Generated by Django 5.2.4 on 2025-08-21 12:59

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('calendar', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('task_type', models.CharField(choices=[('custom', '自定义任务'), ('medication', '吃药提醒')], help_text='任务类型：自定义任务或吃药提醒', max_length=20, verbose_name='任务类型')),
                ('title', models.CharField(blank=True, help_text='自定义任务的标题（自定义任务必填）', max_length=200, verbose_name='任务标题')),
                ('is_all_day', models.Bo<PERSON>anField(default=False, help_text='是否为全天任务', verbose_name='是否全天')),
                ('start_time', models.DateTimeField(blank=True, help_text='任务开始时间', null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, help_text='任务结束时间', null=True, verbose_name='结束时间')),
                ('location', models.CharField(blank=True, help_text='任务地点', max_length=300, verbose_name='地点')),
                ('travel_time', models.DurationField(blank=True, help_text='到达地点所需的出行时间', null=True, verbose_name='出行时间')),
                ('reminder_settings', models.JSONField(blank=True, default=list, help_text='提醒时间设置，格式：[{"minutes": 15, "enabled": true}, {"minutes": 60, "enabled": false}]', verbose_name='提醒设置')),
                ('notes', models.TextField(blank=True, help_text='任务备注信息', verbose_name='备注')),
                ('medicine_name', models.CharField(blank=True, help_text='药物名称（吃药提醒必填）', max_length=200, verbose_name='药物名称')),
                ('dosage_amount', models.DecimalField(blank=True, decimal_places=2, help_text='药物数量', max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='药物数量')),
                ('dosage_unit', models.CharField(blank=True, choices=[('tablet', '片'), ('capsule', '粒'), ('ml', '毫升'), ('mg', '毫克'), ('g', '克'), ('drop', '滴'), ('spoon', '勺'), ('other', '其他')], help_text='药物单位', max_length=20, verbose_name='药物单位')),
                ('medication_time', models.TimeField(blank=True, help_text='每日服药时间', null=True, verbose_name='服药时间')),
                ('reminder_advance_time', models.DurationField(blank=True, help_text='提前多长时间提醒服药', null=True, verbose_name='提前提醒时间')),
                ('weekly_schedule', models.JSONField(blank=True, default=list, help_text='周一到周日的选择，格式：[1,2,3,4,5,6,7] 表示周一到周日', verbose_name='周期设置')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.ForeignKey(help_text='创建该任务的用户', on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '任务',
                'verbose_name_plural': '任务',
                'db_table': 'tasks',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'task_type'], name='tasks_user_id_5316ac_idx'), models.Index(fields=['user', 'start_time'], name='tasks_user_id_59b63c_idx'), models.Index(fields=['user', 'medication_time'], name='tasks_user_id_75d9d8_idx'), models.Index(fields=['task_type', 'start_time'], name='tasks_task_ty_d40caa_idx'), models.Index(fields=['created_at'], name='tasks_created_db4e37_idx')],
            },
        ),
    ]
