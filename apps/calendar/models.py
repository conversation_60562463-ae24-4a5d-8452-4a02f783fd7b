from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from apps.users.models import UserAccount
import uuid
from decimal import Decimal

# 导入图片模型
from .image_models import PrenatalImage, ImageUploadSession


class PrenatalItem(models.Model):
    """标准产检项目表"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField('项目名称', max_length=100)
    content = models.TextField('项目详细内容', blank=True, help_text='详细描述产检项目的内容和注意事项')
    start_week = models.PositiveIntegerField(
        '适用开始孕周',
        blank=True,
        null=True,
        validators=[MinValueValidator(1), MaxValueValidator(42)],
        help_text='适用的开始孕周（1-42周）'
    )
    end_week = models.PositiveIntegerField(
        '适用结束孕周',
        blank=True,
        null=True,
        validators=[MinValueValidator(1), MaxValueValidator(42)],
        help_text='适用的结束孕周（1-42周）'
    )

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'prenatal_items'
        verbose_name = '标准产检项目'
        verbose_name_plural = '标准产检项目'
        ordering = ['start_week', 'name']
        indexes = [
            models.Index(fields=['start_week', 'end_week']),
            models.Index(fields=['name']),
        ]

    def __str__(self):
        if self.start_week and self.end_week:
            return f'{self.name} ({self.start_week}-{self.end_week}周)'
        elif self.start_week:
            return f'{self.name} ({self.start_week}周+)'
        elif self.end_week:
            return f'{self.name} (≤{self.end_week}周)'
        return self.name

    def clean(self):
        """验证孕周范围"""
        from django.core.exceptions import ValidationError
        if self.start_week and self.end_week and self.start_week > self.end_week:
            raise ValidationError('开始孕周不能大于结束孕周')

    def is_applicable_for_week(self, week):
        """检查是否适用于指定孕周"""
        if not week:
            return True
        if self.start_week and week < self.start_week:
            return False
        if self.end_week and week > self.end_week:
            return False
        return True


class CustomPrenatalItem(PrenatalItem):
    """自定义产检项目表 - 继承标准产检项目"""

    created_by = models.ForeignKey(
        UserAccount,
        on_delete=models.CASCADE,
        verbose_name='创建者',
        related_name='custom_prenatal_items',
        help_text='创建该自定义项目的用户'
    )

    class Meta:
        db_table = 'custom_prenatal_items'
        verbose_name = '自定义产检项目'
        verbose_name_plural = '自定义产检项目'
        ordering = ['created_by', 'prenatalitem_ptr__start_week', 'prenatalitem_ptr__name']
        indexes = [
            models.Index(fields=['created_by']),
        ]

    def __str__(self):
        base_str = super().__str__()
        return f'{base_str} (by {self.created_by.email})'


class PrenatalCheckup(models.Model):
    """产检记录表"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        UserAccount,
        on_delete=models.CASCADE,
        verbose_name='用户',
        related_name='prenatal_checkups',
        help_text='进行产检的用户'
    )
    date = models.DateField('产检日期', help_text='预约的产检日期')
    time = models.TimeField('产检时间', help_text='预约的产检时间（小时和分钟）')
    location = models.CharField('产检地点', max_length=200, help_text='医院或诊所名称及地址')

    # 多对多关系
    prenatal_items = models.ManyToManyField(
        PrenatalItem,
        verbose_name='标准产检项目',
        blank=True,
        related_name='standard_checkups',
        help_text='选择的标准产检项目'
    )
    custom_prenatal_items = models.ManyToManyField(
        CustomPrenatalItem,
        verbose_name='自定义产检项目',
        blank=True,
        related_name='custom_checkups',
        help_text='选择的自定义产检项目'
    )

    # 时间相关字段
    travel_time = models.TimeField('出行时间', blank=True, null=True, help_text='计划出发时间')
    reminder_time = models.TimeField('提醒时间', blank=True, null=True, help_text='提醒时间')

    # 笔记字段
    preparation_notes = models.TextField(
        '产检准备笔记',
        blank=True,
        help_text='产检前的准备事项、注意事项等'
    )
    checkup_notes = models.TextField(
        '产检笔记',
        blank=True,
        help_text='产检过程中的记录、医生建议等'
    )

    # 图片字段 - 使用JSONField存储图片URL列表
    checkup_images = models.JSONField(
        '产检单图片',
        default=list,
        blank=True,
        help_text='产检单、B超单等图片的URL列表'
    )


    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'prenatal_checkups'
        verbose_name = '产检记录'
        verbose_name_plural = '产检记录'
        ordering = ['-date', '-time']
        indexes = [
            models.Index(fields=['user', 'date', 'time']),
            models.Index(fields=['date', 'time']),
        ]

    def __str__(self):
        return f'{self.user.email} - {self.date} {self.time.strftime("%H:%M")} - {self.location}'

    def clean(self):
        """验证数据"""
        from django.core.exceptions import ValidationError
        from datetime import datetime

        # 验证产检时间不能是过去时间（创建时）
        if not self.pk and self.date and self.time:
            checkup_datetime = datetime.combine(self.date, self.time)
            if timezone.make_aware(checkup_datetime) < timezone.now():
                raise ValidationError('产检时间不能是过去时间')

        # 验证提醒时间应该在产检时间之前
        if self.reminder_time and self.travel_time and self.reminder_time > self.travel_time:
            raise ValidationError('提醒时间应该在出行时间之前')

    @property
    def is_upcoming(self):
        """是否是即将到来的产检"""
        from datetime import datetime
        if self.date and self.time:
            checkup_datetime = datetime.combine(self.date, self.time)
            return timezone.make_aware(checkup_datetime) > timezone.now()
        return False

    @property
    def is_past_due(self):
        """是否已过期"""
        from datetime import datetime
        if self.date and self.time:
            checkup_datetime = datetime.combine(self.date, self.time)
            return timezone.make_aware(checkup_datetime) < timezone.now()
        return False

    def get_all_items(self):
        """获取所有产检项目（标准+自定义）"""
        standard_items = list(self.prenatal_items.all())
        custom_items = list(self.custom_prenatal_items.all())
        return standard_items + custom_items

    def add_image(self, image_url):
        """添加产检图片"""
        if not self.checkup_images:
            self.checkup_images = []
        self.checkup_images.append(image_url)
        self.save(update_fields=['checkup_images'])

    def remove_image(self, image_url):
        """移除产检图片"""
        if self.checkup_images and image_url in self.checkup_images:
            self.checkup_images.remove(image_url)
            self.save(update_fields=['checkup_images'])


class Event(models.Model):
    """事项/事件管理表"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        UserAccount,
        on_delete=models.CASCADE,
        verbose_name='用户',
        related_name='events',
        help_text='创建该事项的用户'
    )
    title = models.CharField('事项标题', max_length=200, help_text='事项的标题或名称')
    start_time = models.DateTimeField('开始时间', help_text='事项的开始时间')
    end_time = models.DateTimeField('结束时间', help_text='事项的结束时间')
    location = models.CharField(
        '地点',
        max_length=300,
        blank=True,
        help_text='事项发生的地点或位置'
    )
    travel_duration = models.DurationField(
        '出行时间',
        blank=True,
        null=True,
        help_text='到达地点所需的出行时间（小时和分钟）'
    )
    reminder_minutes = models.PositiveIntegerField(
        '提醒时间',
        blank=True,
        null=True,
        help_text='提前多少分钟提醒（分钟数）'
    )
    notes = models.TextField('备注', blank=True, help_text='事项的详细备注或说明')
    date = models.DateField('日期', help_text='事项发生的日期')

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'events'
        verbose_name = '事项'
        verbose_name_plural = '事项'
        ordering = ['date', 'start_time']
        indexes = [
            models.Index(fields=['user', 'date', 'start_time']),
            models.Index(fields=['date', 'start_time']),
            models.Index(fields=['user', 'start_time']),
        ]

    def __str__(self):
        return self.title

    def clean(self):
        """验证数据"""
        from django.core.exceptions import ValidationError

        # 验证结束时间不能早于开始时间
        if self.start_time and self.end_time and self.start_time >= self.end_time:
            raise ValidationError('结束时间必须晚于开始时间')

        # 验证日期与开始时间的日期一致
        if self.date and self.start_time and self.date != self.start_time.date():
            raise ValidationError('日期字段必须与开始时间的日期一致')

        # 验证提醒时间不能为负数
        if self.reminder_minutes is not None and self.reminder_minutes < 0:
            raise ValidationError('提醒时间不能为负数')

    @property
    def is_upcoming(self):
        """是否是即将到来的事项"""
        return self.start_time > timezone.now()

    @property
    def is_past(self):
        """是否已过期"""
        return self.end_time < timezone.now()

    @property
    def is_ongoing(self):
        """是否正在进行中"""
        now = timezone.now()
        return self.start_time <= now <= self.end_time

    @property
    def duration(self):
        """事项持续时间"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None

    def get_reminder_time(self):
        """获取提醒时间"""
        if self.reminder_minutes and self.start_time:
            from datetime import timedelta
            return self.start_time - timedelta(minutes=self.reminder_minutes)
        return None

    def get_departure_time(self):
        """获取出发时间（考虑出行时间）"""
        if self.travel_duration and self.start_time:
            return self.start_time - self.travel_duration
        return None


class Task(models.Model):
    """任务管理模型 - 支持自定义任务和吃药提醒两种类型"""

    class TaskType(models.TextChoices):
        CUSTOM = 'custom', '自定义任务'
        MEDICATION = 'medication', '吃药提醒'

    class DosageUnit(models.TextChoices):
        TABLET = 'tablet', '片'
        CAPSULE = 'capsule', '粒'
        ML = 'ml', '毫升'
        MG = 'mg', '毫克'
        G = 'g', '克'
        DROP = 'drop', '滴'
        SPOON = 'spoon', '勺'
        OTHER = 'other', '其他'

    # 基础字段
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        UserAccount,
        on_delete=models.CASCADE,
        verbose_name='用户',
        related_name='tasks',
        help_text='创建该任务的用户'
    )
    task_type = models.CharField(
        '任务类型',
        max_length=20,
        choices=TaskType.choices,
        help_text='任务类型：自定义任务或吃药提醒'
    )

    # 自定义任务字段
    title = models.CharField(
        '任务标题',
        max_length=200,
        blank=True,
        help_text='自定义任务的标题（自定义任务必填）'
    )
    is_all_day = models.BooleanField(
        '是否全天',
        default=False,
        help_text='是否为全天任务'
    )
    start_time = models.DateTimeField(
        '开始时间',
        blank=True,
        null=True,
        help_text='任务开始时间'
    )
    end_time = models.DateTimeField(
        '结束时间',
        blank=True,
        null=True,
        help_text='任务结束时间'
    )
    location = models.CharField(
        '地点',
        max_length=300,
        blank=True,
        help_text='任务地点'
    )
    travel_time = models.DurationField(
        '出行时间',
        blank=True,
        null=True,
        help_text='到达地点所需的出行时间'
    )
    reminder_settings = models.JSONField(
        '提醒设置',
        default=list,
        blank=True,
        help_text='提醒时间设置，格式：[{"minutes": 15, "enabled": true}, {"minutes": 60, "enabled": false}]'
    )
    notes = models.TextField(
        '备注',
        blank=True,
        help_text='任务备注信息'
    )

    # 吃药提醒字段
    medicine_name = models.CharField(
        '药物名称',
        max_length=200,
        blank=True,
        help_text='药物名称（吃药提醒必填）'
    )
    dosage_amount = models.DecimalField(
        '药物数量',
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text='药物数量'
    )
    dosage_unit = models.CharField(
        '药物单位',
        max_length=20,
        choices=DosageUnit.choices,
        blank=True,
        help_text='药物单位'
    )
    medication_time = models.TimeField(
        '服药时间',
        blank=True,
        null=True,
        help_text='每日服药时间'
    )
    reminder_advance_time = models.DurationField(
        '提前提醒时间',
        blank=True,
        null=True,
        help_text='提前多长时间提醒服药'
    )
    weekly_schedule = models.JSONField(
        '周期设置',
        default=list,
        blank=True,
        help_text='周一到周日的选择，格式：[1,2,3,4,5,6,7] 表示周一到周日'
    )

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'tasks'
        verbose_name = '任务'
        verbose_name_plural = '任务'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'task_type']),
            models.Index(fields=['user', 'start_time']),
            models.Index(fields=['user', 'medication_time']),
            models.Index(fields=['task_type', 'start_time']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        if self.task_type == self.TaskType.CUSTOM:
            return f'{self.title} ({self.get_task_type_display()})'
        elif self.task_type == self.TaskType.MEDICATION:
            return f'{self.medicine_name} ({self.get_task_type_display()})'
        return f'任务 - {self.get_task_type_display()}'

    def clean(self):
        """验证数据"""
        from django.core.exceptions import ValidationError

        # 自定义任务验证
        if self.task_type == self.TaskType.CUSTOM:
            if not self.title:
                raise ValidationError('自定义任务必须填写标题')

            if not self.is_all_day:
                if not self.start_time:
                    raise ValidationError('非全天任务必须设置开始时间')
                if not self.end_time:
                    raise ValidationError('非全天任务必须设置结束时间')
                if self.start_time and self.end_time and self.start_time >= self.end_time:
                    raise ValidationError('结束时间必须晚于开始时间')

        # 吃药提醒验证
        elif self.task_type == self.TaskType.MEDICATION:
            if not self.medicine_name:
                raise ValidationError('吃药提醒必须填写药物名称')
            if not self.medication_time:
                raise ValidationError('吃药提醒必须设置服药时间')
            if self.dosage_amount is not None and self.dosage_amount <= 0:
                raise ValidationError('药物数量必须大于0')
            if not self.weekly_schedule:
                raise ValidationError('吃药提醒必须设置周期')

            # 验证周期设置格式
            if not isinstance(self.weekly_schedule, list):
                raise ValidationError('周期设置必须是列表格式')
            for day in self.weekly_schedule:
                if not isinstance(day, int) or day < 1 or day > 7:
                    raise ValidationError('周期设置必须是1-7的整数列表（1=周一，7=周日）')

        # 验证提醒设置格式
        if self.reminder_settings:
            if not isinstance(self.reminder_settings, list):
                raise ValidationError('提醒设置必须是列表格式')
            for reminder in self.reminder_settings:
                if not isinstance(reminder, dict):
                    raise ValidationError('提醒设置项必须是字典格式')
                if 'minutes' not in reminder or 'enabled' not in reminder:
                    raise ValidationError('提醒设置项必须包含minutes和enabled字段')
                if not isinstance(reminder['minutes'], int) or reminder['minutes'] < 0:
                    raise ValidationError('提醒时间必须是非负整数')
                if not isinstance(reminder['enabled'], bool):
                    raise ValidationError('提醒启用状态必须是布尔值')

    @property
    def is_upcoming(self):
        """是否是即将到来的任务"""
        if self.task_type == self.TaskType.CUSTOM and self.start_time:
            return self.start_time > timezone.now()
        elif self.task_type == self.TaskType.MEDICATION and self.medication_time:
            # 对于吃药提醒，检查今天是否在周期内且时间未过
            from datetime import datetime, date
            now = timezone.now()
            today = now.date()
            current_time = now.time()

            # 检查今天是否在周期内（1=周一，7=周日）
            today_weekday = today.isoweekday()  # 1=周一，7=周日
            if today_weekday in self.weekly_schedule:
                return self.medication_time > current_time
        return False

    @property
    def is_past(self):
        """是否已过期"""
        if self.task_type == self.TaskType.CUSTOM and self.end_time:
            return self.end_time < timezone.now()
        elif self.task_type == self.TaskType.MEDICATION and self.medication_time:
            # 对于吃药提醒，检查今天是否在周期内且时间已过
            from datetime import datetime, date
            now = timezone.now()
            today = now.date()
            current_time = now.time()

            # 检查今天是否在周期内
            today_weekday = today.isoweekday()
            if today_weekday in self.weekly_schedule:
                return self.medication_time < current_time
        return False

    @property
    def is_ongoing(self):
        """是否正在进行中（仅适用于自定义任务）"""
        if self.task_type == self.TaskType.CUSTOM and self.start_time and self.end_time:
            now = timezone.now()
            return self.start_time <= now <= self.end_time
        return False

    @property
    def duration(self):
        """任务持续时间（仅适用于自定义任务）"""
        if self.task_type == self.TaskType.CUSTOM and self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None

    def get_next_reminder_time(self):
        """获取下次提醒时间"""
        if self.task_type == self.TaskType.CUSTOM and self.start_time and self.reminder_settings:
            # 获取启用的提醒设置
            enabled_reminders = [r for r in self.reminder_settings if r.get('enabled', False)]
            if enabled_reminders:
                from datetime import timedelta
                # 返回最近的提醒时间
                min_minutes = min(r['minutes'] for r in enabled_reminders)
                return self.start_time - timedelta(minutes=min_minutes)

        elif self.task_type == self.TaskType.MEDICATION and self.medication_time and self.reminder_advance_time:
            from datetime import datetime, timedelta
            now = timezone.now()
            today = now.date()

            # 检查今天是否在周期内
            today_weekday = today.isoweekday()
            if today_weekday in self.weekly_schedule:
                medication_datetime = datetime.combine(today, self.medication_time)
                medication_datetime = timezone.make_aware(medication_datetime)
                return medication_datetime - self.reminder_advance_time

        return None

    def get_departure_time(self):
        """获取出发时间（仅适用于自定义任务）"""
        if self.task_type == self.TaskType.CUSTOM and self.travel_time and self.start_time:
            return self.start_time - self.travel_time
        return None

    def get_weekly_schedule_display(self):
        """获取周期设置的显示文本"""
        if not self.weekly_schedule:
            return ''

        weekday_names = {
            1: '周一', 2: '周二', 3: '周三', 4: '周四',
            5: '周五', 6: '周六', 7: '周日'
        }

        if len(self.weekly_schedule) == 7:
            return '每天'
        elif set(self.weekly_schedule) == {1, 2, 3, 4, 5}:
            return '工作日'
        elif set(self.weekly_schedule) == {6, 7}:
            return '周末'
        else:
            return '、'.join([weekday_names[day] for day in sorted(self.weekly_schedule)])

    def get_dosage_display(self):
        """获取药物剂量的显示文本"""
        if self.task_type == self.TaskType.MEDICATION and self.dosage_amount and self.dosage_unit:
            return f'{self.dosage_amount}{self.get_dosage_unit_display()}'
        return ''
