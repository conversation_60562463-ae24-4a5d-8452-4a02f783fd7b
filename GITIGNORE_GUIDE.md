# Git 忽略文件配置指南

## 📋 概述

本文档说明了 Align Backend 项目中 `.gitignore` 文件的配置，特别是针对 Django 项目的最佳实践。

## 🎯 主要忽略的内容

### 1. Django 特定文件

```gitignore
# Django media files (用户上传文件) - 重要：忽略所有用户上传的文件
media/
!media/.gitkeep

# Django static files (静态文件收集目录)
staticfiles/
static_root/
collected_static/

# Django 数据库文件
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Django 缓存和会话
django_cache/
django_session/

# Django 敏感配置
secret_key.txt
.secret_key
```

### 2. 为什么要忽略 media/ 文件夹？

**安全原因：**
- 用户上传的文件可能包含敏感信息（医疗报告、个人照片等）
- 避免意外提交私人数据到公共仓库

**性能原因：**
- 媒体文件通常很大，会显著增加仓库大小
- 克隆和拉取操作会变得很慢

**实用原因：**
- 不同环境（开发/测试/生产）的媒体文件不同
- 媒体文件应该通过备份系统而不是版本控制管理

### 3. media/.gitkeep 文件的作用

```bash
# 保持 media 目录结构但忽略内容
media/
!media/.gitkeep
```

- 确保 media 目录在版本控制中存在
- 新克隆的项目会有正确的目录结构
- Django 可以正常创建上传文件

## 🔧 Docker 相关忽略

```gitignore
# Docker 数据和日志（保留配置文件）
docker/*/data/
docker/*/logs/
docker/mariadb/data/
docker/nginx/logs/
docker-compose.override.yml
.docker/
```

## 📁 当前项目结构

```
align-backend/
├── media/                    # 被忽略（除了 .gitkeep）
│   ├── .gitkeep             # 被跟踪
│   └── prenatal/            # 被忽略
│       └── images/          # 被忽略
│           └── 2025/08/     # 被忽略（24个图片文件）
├── staticfiles/             # 被忽略
├── docker/
│   └── mariadb/
│       └── data/            # 被忽略
└── logs/                    # 被忽略
```

## ✅ 验证配置

检查文件是否被正确忽略：

```bash
# 查看被忽略的文件
git status --ignored

# 检查特定文件是否被忽略
git check-ignore media/prenatal/images/2025/08/*.jpg

# 强制添加被忽略目录中的特定文件
git add -f media/.gitkeep
```

## 🚀 最佳实践

1. **定期检查**：确保敏感文件没有被意外提交
2. **环境变量**：使用 `.env` 文件存储敏感配置（也被忽略）
3. **备份策略**：为 media 文件建立独立的备份机制
4. **生产部署**：使用云存储（如 AWS S3）存储媒体文件

## 🔒 安全提醒

- 永远不要提交包含真实用户数据的文件
- 定期审查 `.gitignore` 配置
- 在生产环境中使用专门的媒体文件存储服务
- 确保备份策略覆盖所有重要的用户数据
